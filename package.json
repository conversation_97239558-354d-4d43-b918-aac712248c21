{"private": true, "workspaces": {"packages": ["packages/*"], "nohoist": ["**/css-modules-typescript-loader", "**/@alifc/theme-lowcode-*", "**/jest"]}, "scripts": {"build": "./scripts/build.sh", "build:npm": "lerna run build --stream", "build:umd": "lerna run build:umd --stream", "clean": "rimraf ./packages/*/lib ./packages/*/es ./packages/*/dist ./packages/*/build", "clean:lib": "rimraf ./node_modules", "lint": "f2elint scan -q -i ./packages/*/src", "lint:fix": "f2elint fix -i ./packages/*/src", "lint:modules": "f2elint scan -q -i ./modules/*/src", "lint:modules:fix": "f2elint fix -i ./modules/*/src", "pub": "npm run watchdog:build && lerna publish patch --yes --force-publish --exact --no-changelog", "pub:minor": "npm run watchdog:build && lerna publish minor --yes --force-publish --exact --no-changelog", "pub:major": "npm run watchdog:build && lerna publish major --yes --force-publish --exact --no-changelog", "pub:premajor": "npm run watchdog:build && lerna publish premajor --force-publish --exact --dist-tag beta --preid beta --no-changelog", "pub:preminor": "npm run watchdog:build && lerna publish preminor --force-publish --exact --dist-tag beta --preid beta --no-changelog", "pub:prepatch": "npm run watchdog:build && lerna publish prepatch --force-publish --exact --dist-tag beta --preid beta --no-changelog", "pub:prerelease": "npm run watchdog:build && lerna publish prerelease --yes --force-publish --exact --dist-tag beta --preid beta --no-changelog", "setup": "node ./scripts/setup.js", "setup:test": "./scripts/setup-for-test.sh", "setup:skip-build": "./scripts/setup-skip-build.sh", "start": "node ./scripts/start.js", "test": "lerna run test --stream", "test:snapshot": "lerna run test:snapshot", "watchdog:build": "node ./scripts/watchdog.js", "sync": "./scripts/sync.sh", "syncOss": "node ./scripts/sync-oss.js"}, "husky": {"hooks": {"pre-commit": "f2elint commit-file-scan", "commit-msg": "f2elint commit-msg-scan"}}, "devDependencies": {"del": "^6.1.1", "execa": "^5.1.1", "f2elint": "^2.0.1", "gulp": "^4.0.2", "husky": "^7.0.4", "lerna": "^4.0.0", "typescript": "4.6.2", "yarn": "^1.22.17", "rimraf": "^3.0.2", "@types/react-router": "5.1.18", "@alilc/build-plugin-lce": "^0.0.5", "babel-jest": "^26.5.2", "@alilc/lowcode-test-mate": "^1.0.1"}, "engines": {"node": ">=14.17.0 <18"}, "tnpm": {"mode": "yarn", "lockfile": "enable"}, "resolutions": {"typescript": "4.6.2", "react-error-overlay": "6.0.9"}, "repository": "**************:alibaba/lowcode-engine.git"}