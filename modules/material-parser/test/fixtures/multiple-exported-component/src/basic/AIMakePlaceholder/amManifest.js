/**
 * The template of manifest for AiMake studio.
 */
const manifest = {
  // The name of current component.
  name: 'AIMakePlaceholder',
  // The description of current component.
  description: '占位图',
  // The coverimage's url of current component.
  coverImage:
    'https://img.alicdn.com/tfs/TB1RxDup3mTBuNjy1XbXXaMrVXa-362-120.png',
  // The category of current component in AiMake studio.
  // can be:
  // `分栏` or `文本` or `按钮` or `标签` or `标签页` or `表格` or `单选` or `分割线`
  // or `分页` or `复选` or `滑动条` or `进度条` or `卡片` or `开关` or `缺省状态`
  // or `日期选择` or `输入框` or `搜索框` or `图表` or `图片` or `下拉选择` or `表单行`
  // or `树控件` or `折叠面板` or `占位图`
  category: '占位图',
  // The preview list of current component in AiMake studio.
  // Each preset contains following keys:
  // - `alias`: string. required. The previewing component's name to display
  // - `thumbnail`: string. not required. The previewing component's thumbnail
  // - `customProps`: object. not required.
  // The previewing component's customize props, e.g. { [propName]: [propValue] }
  // - `colSpan`: number. not required. default 24 (1~24). The previewing component's size when to display
  presets: [
    {
      alias: '占位图',
      thumbnail:
        'https://img.alicdn.com/tfs/TB1RxDup3mTBuNjy1XbXXaMrVXa-362-120.png',
      colSpan: 24,
      customProps: {
        width: '224px',
        height: '126px',
        backgroundColor: '#FFF6E0',
        textAlign: 'center',
        border: '1px dashed rgb(170, 170, 170)',
        children: '暂不支持此组件',
      },
    },
  ],
  // Other settings of current component for AiMake studio.
  settings: {
    // The render type of current component in AiMake studio.
    // can be:
    // `element_inline` or `element_block` or `container`
    type: 'element_inline',
    // The insert mode of current component in AiMake studio.
    // can be:
    // one or combine of `t` and `b` and `r` and `l`
    insertionModes: 'lr',
    // The handle list of current component in AiMake studio.
    // can be:
    // an array contains one and more of ['cut', 'copy', 'paste', 'delete', 'duplicate']
    handles: ['cut', 'copy', 'paste', 'delete', 'duplicate'],
    // Whether the component can be actived.
    shouldActive: true,
    // Whether the component can be dragged.
    shouldDrag: true,
    // The props of current component in AiMake studio.
    // Each property contains following keys:
    // - `name`: string. required. The property's name
    // - `label`: string. required. The property's name to display
    // - `renderer`: string. required. The property's editor. can be: (@冰骊)
    // - `defaultValue`: any. not required. The property's default value
    // - `params`: any. not required. The parameters for property's editor
    // - `placeholder`: string. not required. The placeholder for property's editor
    // - `hint`: string. not required. The hint for property's editor
    props: [
      {
        name: 'margin',
        label: '外边距',
        renderer: 'Quadrant',
      },
      {
        name: 'width',
        label: '宽度',
        defaultValue: '224px',
        renderer: 'Width',
      },
      {
        name: 'height',
        label: '高度',
        defaultValue: '126px',
        renderer: 'Height',
      },
      {
        name: 'backgroundColor',
        label: '背景色',
        defaultValue: '#FFF6E0',
        renderer: false,
      },
      {
        name: 'textAlign',
        label: '对齐',
        defaultValue: 'center',
        renderer: false,
      },
      {
        name: 'border',
        label: '边框',
        defaultValue: '1px dashed rgb(170, 170, 170)',
        renderer: false,
      },
      {
        name: 'children',
        label: '内容',
        defaultValue: '暂不支持此组件',
        renderer: false,
      },
    ],
  },
};

export default manifest;
