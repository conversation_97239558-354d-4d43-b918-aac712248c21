{"name": "icejs-demo-app", "version": "0.1.5", "description": "轻量级模板，使用 JavaScript，仅包含基础的 Layout。", "dependencies": {"moment": "^2.24.0", "react": "^16.4.1", "react-dom": "^16.4.1", "react-router": "^5.2.1", "@alifd/theme-design-pro": "^0.x", "intl-messageformat": "^9.3.6", "@ice/store": "^1.4.3", "@loadable/component": "^5.15.2", "@alilc/lowcode-datasource-engine": "^1.0.0", "@alilc/lowcode-datasource-url-params-handler": "^1.0.0", "@alilc/lowcode-datasource-fetch-handler": "^1.0.0", "@alifd/next": "1.19.18"}, "devDependencies": {"@ice/spec": "^1.0.0", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "eslint": "^6.0.1", "ice.js": "^1.0.0", "stylelint": "^13.2.0"}, "scripts": {"start": "icejs start", "build": "icejs build", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint --cache --ext .js,.jsx ./", "stylelint": "stylelint ./**/*.scss"}, "ideMode": {"name": "ice-react"}, "iceworks": {"type": "react", "adapter": "adapter-react-v3"}, "engines": {"node": ">=8.0.0"}, "repository": {"type": "git", "url": "http://gitlab.xxx.com/msd/leak-scan/tree/master"}, "private": true, "originTemplate": "@alifd/scaffold-lite-js"}