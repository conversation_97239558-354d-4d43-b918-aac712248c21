{"name": "icejs3-demo-app", "version": "0.1.5", "description": "icejs 3 轻量级模板，使用 JavaScript，仅包含基础的 Layout。", "dependencies": {"moment": "^2.24.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^6.9.0", "react-router-dom": "^6.9.0", "intl-messageformat": "^9.3.6", "@alifd/next": "1.19.18", "@ice/runtime": "~1.1.0", "@alilc/lowcode-datasource-engine": "^1.0.0"}, "devDependencies": {"@ice/app": "~3.1.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/node": "^18.11.17", "@ice/plugin-fusion": "^1.0.1", "@ice/plugin-moment-locales": "^1.0.0", "eslint": "^6.0.1", "stylelint": "^13.2.0"}, "scripts": {"start": "ice start", "build": "ice build", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint --cache --ext .js,.jsx ./", "stylelint": "stylelint ./**/*.scss"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "http://gitlab.xxx.com/msd/leak-scan/tree/master"}, "private": true, "originTemplate": "@alifd/scaffold-lite-js"}