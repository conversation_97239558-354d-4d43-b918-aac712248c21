{"name": "@alilc/lowcode-renderer-core", "version": "1.3.2", "description": "renderer core", "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "files": ["lib", "es"], "scripts": {"build": "build-scripts build", "test": "build-scripts test --config build.test.json", "test:cov": "build-scripts test --config build.test.json --jest-coverage"}, "dependencies": {"@alilc/lowcode-datasource-engine": "^1.0.0", "@alilc/lowcode-types": "1.3.2", "@alilc/lowcode-utils": "1.3.2", "classnames": "^2.2.6", "debug": "^4.1.1", "fetch-jsonp": "^1.1.3", "intl-messageformat": "^9.3.1", "jsonuri": "^2.1.2", "lodash": "^4.17.11", "prop-types": "^15.7.2", "react-is": "^16.10.1", "socket.io-client": "^2.2.0", "whatwg-fetch": "^3.0.0"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@alifd/next": "^1.26.0", "@alilc/lowcode-designer": "1.3.2", "@babel/plugin-transform-typescript": "^7.16.8", "@testing-library/react": "^11.2.2", "@types/classnames": "^2.2.11", "@types/debug": "^4.1.5", "@types/jest": "^26.0.16", "@types/lodash": "^4.14.167", "@types/node": "^13.7.1", "@types/prop-types": "^15.7.3", "@types/react-is": "^17.0.3", "@types/react-test-renderer": "^17.0.1", "jest": "^26.6.3", "react-test-renderer": "^17.0.2", "ts-jest": "^26.5.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "http", "url": "https://github.com/alibaba/lowcode-engine/tree/main/packages/renderer-core"}, "gitHead": "2669f179e6f899d395ce1942d0fe04f9c5ed48a6", "bugs": "https://github.com/alibaba/lowcode-engine/issues", "homepage": "https://github.com/alibaba/lowcode-engine/#readme"}