export const sampleSchema = {
  "componentName": "Page",
  "id": "node_ockyigdqxl1",
  "docId": "dockyigdqxl",
  "props": {
      "templateVersion": "1.0.0",
      "containerStyle": {},
      "pageStyle": {
          "backgroundColor": "#f2f3f5"
      },
      "className": "_css_pseudo_node_ockyigdqxl1"
  },
  "dataSource": {
      "offline": [],
      "globalConfig": {},
      "online": [
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
              "formUuid": "FORM-3KYJN7RV-J47BPFK63W2PHAGPO1VC3-B4H1WE5K-131",
              "name": "locale",
              "description": "当前语种（在 window.g_config 中设置）",
              "id": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
              "protocal": "VALUE",
              "shareType": "APP"
          },
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
              "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
              "name": "appType",
              "description": "应用的唯一 code",
              "id": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
              "protocal": "VALUE",
              "shareType": "APP"
          },
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
              "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
              "name": "version",
              "description": "应该版本，默认 0.1.0",
              "id": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
              "protocal": "VALUE",
              "shareType": "APP"
          },
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
              "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
              "name": "apiPrefix",
              "description": "",
              "id": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
              "protocal": "VALUE",
              "shareType": "APP"
          }
      ],
      "sync": true,
      "list": [
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
              "formUuid": "FORM-3KYJN7RV-J47BPFK63W2PHAGPO1VC3-B4H1WE5K-131",
              "name": "locale",
              "description": "当前语种（在 window.g_config 中设置）",
              "id": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
              "protocal": "VALUE",
              "shareType": "APP"
          },
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
              "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
              "name": "appType",
              "description": "应用的唯一 code",
              "id": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
              "protocal": "VALUE",
              "shareType": "APP"
          },
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
              "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
              "name": "version",
              "description": "应该版本，默认 0.1.0",
              "id": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
              "protocal": "VALUE",
              "shareType": "APP"
          },
          {
              "gmtModified": 1639385418000,
              "initialData": "",
              "globalUid": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
              "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
              "name": "apiPrefix",
              "description": "",
              "id": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
              "protocal": "VALUE",
              "shareType": "APP"
          }
      ]
  },
  "methods": {},
  "lifeCycles": {},
  "hidden": false,
  "title": "",
  "isLocked": false,
  "condition": true,
  "conditionGroup": "",
  "children": [
      {
          "componentName": "RootHeader",
          "id": "node_ockyigdqxl2",
          "docId": "dockyigdqxl",
          "props": {},
          "hidden": false,
          "title": "",
          "isLocked": false,
          "condition": true,
          "conditionGroup": ""
      },
      {
          "componentName": "RootContent",
          "id": "node_ockyigdqxl3",
          "docId": "dockyigdqxl",
          "props": {
              "contentMargin": "20",
              "contentPadding": "20",
              "contentBgColor": "white"
          },
          "hidden": false,
          "title": "",
          "isLocked": false,
          "condition": true,
          "conditionGroup": "",
          "children": [
              {
                  "componentName": "Button",
                  "id": "node_ockyigdqxl5",
                  "docId": "dockyigdqxl",
                  "props": {
                      "content": "按 钮",
                      "type": "primary",
                      "size": "medium",
                      "behavior": "NORMAL",
                      "__style__": {},
                      "fieldId": "button_kyige3yf",
                      "events": {
                          "ignored": true
                      },
                      "baseIcon": "",
                      "otherIcon": "",
                      "loading": false,
                      "triggerEventsWhenLoading": false,
                      "className": "_css_pseudo_node_ockyigdqxl5"
                  },
                  "hidden": false,
                  "title": "",
                  "isLocked": false,
                  "condition": true,
                  "conditionGroup": ""
              }
          ]
      },
      {
          "componentName": "RootFooter",
          "id": "node_ockyigdqxl4",
          "docId": "dockyigdqxl",
          "props": {},
          "hidden": false,
          "title": "",
          "isLocked": false,
          "condition": true,
          "conditionGroup": ""
      }
  ]
};