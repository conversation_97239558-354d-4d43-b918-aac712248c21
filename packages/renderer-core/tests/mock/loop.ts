const schema = {
    "componentName": "Page",
    "id": "node_ocl1djd9o41",
    "docId": "docl1djd9o4",
    "props": {
        "templateVersion": "1.0.0",
        "containerStyle": {},
        "pageStyle": {
            "backgroundColor": "#f2f3f5"
        },
        "className": "_css_pseudo_node_ocl1djd9o41"
    },
    "dataSource": {
        "offline": [],
        "globalConfig": {},
        "online": [
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
                "formUuid": "FORM-3KYJN7RV-J47BPFK63W2PHAGPO1VC3-B4H1WE5K-131",
                "name": "locale",
                "description": "当前语种（在 window.g_config 中设置）",
                "id": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
                "protocal": "VALUE",
                "shareType": "APP"
            },
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
                "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
                "name": "appType",
                "description": "应用的唯一 code",
                "id": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
                "protocal": "VALUE",
                "shareType": "APP"
            },
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
                "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
                "name": "version",
                "description": "应该版本，默认 0.1.0",
                "id": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
                "protocal": "VALUE",
                "shareType": "APP"
            },
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
                "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
                "name": "apiPrefix",
                "description": "",
                "id": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
                "protocal": "VALUE",
                "shareType": "APP"
            }
        ],
        "sync": true,
        "list": [
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
                "formUuid": "FORM-3KYJN7RV-J47BPFK63W2PHAGPO1VC3-B4H1WE5K-131",
                "name": "locale",
                "description": "当前语种（在 window.g_config 中设置）",
                "id": "AY866BC1ERSVK0BE55NU364515LH3NM0RF4XK61",
                "protocal": "VALUE",
                "shareType": "APP"
            },
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
                "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
                "name": "appType",
                "description": "应用的唯一 code",
                "id": "AY866BC1ERSVK0BE55NU364515LH3SM0RF4XK71",
                "protocal": "VALUE",
                "shareType": "APP"
            },
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
                "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
                "name": "version",
                "description": "应该版本，默认 0.1.0",
                "id": "AY866BC1ERSVK0BE55NU364515LH3XM0RF4XK81",
                "protocal": "VALUE",
                "shareType": "APP"
            },
            {
                "gmtModified": 1639385418000,
                "initialData": "",
                "globalUid": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
                "formUuid": "FORM-RFYJTWKV-D47BWO6R0QHA74R062FN2-R5IPXK4K-0H",
                "name": "apiPrefix",
                "description": "",
                "id": "AY866BC1ERSVK0BE55NU364515LH33N0RF4XK91",
                "protocal": "VALUE",
                "shareType": "APP"
            }
        ]
    },
    "methods": {},
    "hidden": false,
    "title": "",
    "isLocked": false,
    "condition": true,
    "conditionGroup": "",
    "children": [
        {
            "componentName": "RootHeader",
            "id": "node_ocl1djd9o42",
            "docId": "docl1djd9o4",
            "props": {},
            "hidden": false,
            "title": "",
            "isLocked": false,
            "condition": true,
            "conditionGroup": ""
        },
        {
            "componentName": "RootContent",
            "id": "node_ocl1djd9o43",
            "docId": "docl1djd9o4",
            "props": {
                "contentMargin": "20",
                "contentPadding": "20",
                "contentBgColor": "white"
            },
            "hidden": false,
            "title": "",
            "isLocked": false,
            "condition": true,
            "conditionGroup": "",
            "children": [
                {
                    "componentName": "Div",
                    "id": "node_ocl1djd9o45",
                    "docId": "docl1djd9o4",
                    "props": {
                        "behavior": "NORMAL",
                        "__style__": {},
                        "fieldId": "div_l1djdj1n",
                        "events": {
                            "ignored": true
                        },
                        "useFieldIdAsDomId": false,
                        "customClassName": "",
                        "className": "_css_pseudo_node_ocl1djd9o45"
                    },
                    "hidden": false,
                    "title": "",
                    "isLocked": false,
                    "condition": true,
                    "conditionGroup": "",
                    "loop": [
                        1,
                        2,
                        3
                    ],
                    "loopArgs": [
                        null,
                        null
                    ],
                    "children": [
                        {
                            "componentName": "Div",
                            "id": "node_ocl1djd9o46",
                            "docId": "docl1djd9o4",
                            "props": {
                                "behavior": "NORMAL",
                                "__style__": {},
                                "fieldId": "div_l1djdj1o",
                                "events": {
                                    "ignored": true
                                },
                                "useFieldIdAsDomId": false,
                                "customClassName": "",
                                "className": "_css_pseudo_node_ocl1djd9o46"
                            },
                            "hidden": false,
                            "title": "",
                            "isLocked": false,
                            "condition": true,
                            "conditionGroup": "",
                            "loop": [
                                1,
                                2,
                                3
                            ],
                            "loopArgs": [
                                null,
                                null
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "componentName": "RootFooter",
            "id": "node_ocl1djd9o44",
            "docId": "docl1djd9o4",
            "props": {},
            "hidden": false,
            "title": "",
            "isLocked": false,
            "condition": true,
            "conditionGroup": ""
        }
    ]
};

export default schema;
