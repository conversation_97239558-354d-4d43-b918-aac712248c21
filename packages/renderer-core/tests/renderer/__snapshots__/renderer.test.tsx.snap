// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Base Render renderComp 1`] = `
<div
  className="lce-page lce-test"
  style={
    Object {
      "padding": "0 5px 0 5px",
    }
  }
>
  <div
    __id="node_dockcy8n9xed"
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="next-box"
    style={
      Object {
        "backgroundColor": "rgba(31,56,88,0.1)",
        "flexDirection": "column",
        "flexWrap": "nowrap",
        "msFlexDirection": "column",
        "msFlexWrap": "none",
        "padding": "12px 12px 12px 12px",
      }
    }
  >
    <div
      __id="node_dockcy8n9xee"
      __inner__={
        Object {
          "condition": true,
          "hidden": undefined,
        }
      }
      className="next-box"
      style={
        Object {
          "backgroundColor": "#ffffff",
          "flexDirection": "column",
          "flexWrap": "nowrap",
          "msFlexDirection": "column",
          "msFlexWrap": "none",
          "padding": "12px 12px 12px 12px",
        }
      }
    >
      <nav
        __id="node_dockcy8n9xef"
        __inner__={
          Object {
            "condition": true,
            "hidden": undefined,
          }
        }
        aria-label="Breadcrumb"
        style={
          Object {
            "position": "relative",
          }
        }
      >
        <ul
          className="next-breadcrumb"
        >
          <li
            className="next-breadcrumb-item"
            dir={null}
          >
            <span
              __id="node_dockcy8n9xeg"
              __inner__={
                Object {
                  "condition": true,
                  "hidden": undefined,
                }
              }
              className="next-breadcrumb-text"
            >
              首页
            </span>
            <span
              className="next-breadcrumb-separator"
            >
              <i
                className="next-icon next-icon-arrow-right next-medium next-breadcrumb-icon-sep"
                style={Object {}}
              />
            </span>
          </li>
          <li
            className="next-breadcrumb-item"
            dir={null}
          >
            <span
              __id="node_dockcy8n9xei"
              __inner__={
                Object {
                  "condition": true,
                  "hidden": undefined,
                }
              }
              className="next-breadcrumb-text"
            >
              品质中台
            </span>
            <span
              className="next-breadcrumb-separator"
            >
              <i
                className="next-icon next-icon-arrow-right next-medium next-breadcrumb-icon-sep"
                style={Object {}}
              />
            </span>
          </li>
          <li
            className="next-breadcrumb-item"
            dir={null}
          >
            <span
              __id="node_dockcy8n9xek"
              __inner__={
                Object {
                  "condition": true,
                  "hidden": undefined,
                }
              }
              className="next-breadcrumb-text"
            >
              商家品质页面管理
            </span>
            <span
              className="next-breadcrumb-separator"
            >
              <i
                className="next-icon next-icon-arrow-right next-medium next-breadcrumb-icon-sep"
                style={Object {}}
              />
            </span>
          </li>
          <li
            className="next-breadcrumb-item"
            dir={null}
          >
            <span
              __id="node_dockcy8n9xem"
              __inner__={
                Object {
                  "condition": true,
                  "hidden": undefined,
                }
              }
              aria-current="page"
              className="next-breadcrumb-text activated"
            >
              质检知识条配置
            </span>
          </li>
        </ul>
      </nav>
    </div>
    <div
      __id="node_dockcy8n9xeo"
      __inner__={
        Object {
          "condition": true,
          "hidden": undefined,
        }
      }
      className="next-box"
      style={
        Object {
          "backgroundColor": "#ffffff",
          "flexDirection": "column",
          "flexWrap": "nowrap",
          "marginTop": "12px",
          "msFlexDirection": "column",
          "msFlexWrap": "none",
        }
      }
    >
      <form
        __events={Array []}
        __id="node_dockcy8n9xep"
        __inner__={
          Object {
            "condition": true,
            "hidden": undefined,
          }
        }
        className="next-form next-inline next-medium"
        onSubmit={[Function]}
        role="grid"
        style={
          Object {
            "marginLeft": "12px",
            "marginRight": "12px",
            "marginTop": "12px",
          }
        }
      >
        <div
          __id="node_dockcy8n9xeq"
          __inner__={
            Object {
              "condition": true,
              "hidden": undefined,
            }
          }
          className="next-form-item next-left next-medium"
          style={
            Object {
              "marginBottom": "0",
            }
          }
        >
          <div
            className="next-form-item-label"
          >
            <label>
              类目名：
            </label>
          </div>
          <div
            className="next-form-item-control"
          >
            <span
              aria-haspopup={true}
              className="next-select next-select-trigger next-select-single next-medium next-inactive next-no-search"
              onClick={[Function]}
              onKeyDown={[Function]}
              onMouseDown={[Function]}
              onMouseEnter={[Function]}
              onMouseLeave={[Function]}
              style={
                Object {
                  "width": "150px",
                }
              }
            >
              <span
                className="next-input next-medium next-select-inner"
              >
                <span
                  className="next-select-values next-input-text-field"
                >
                  <span
                    className="next-select-trigger-search"
                  >
                    <input
                      __id="node_dockcy8n9xer"
                      __inner__={
                        Object {
                          "condition": true,
                          "hidden": undefined,
                        }
                      }
                      autoComplete="off"
                      disabled={false}
                      height="100%"
                      maxLength={null}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onCompositionEnd={[Function]}
                      onCompositionStart={[Function]}
                      onFocus={[Function]}
                      onKeyDown={[Function]}
                      placeholder="请选择"
                      readOnly={true}
                      role="combobox"
                      size="1"
                      tabIndex={0}
                      value=""
                    />
                    <span
                      aria-hidden={true}
                    >
                      <span>
                        请选择
                      </span>
                      <span
                        style={
                          Object {
                            "display": "inline-block",
                            "width": 1,
                          }
                        }
                      >
                         
                      </span>
                    </span>
                  </span>
                </span>
                <span
                  className="next-input-control"
                  onClick={[Function]}
                >
                  <span
                    aria-hidden={true}
                    className="next-select-arrow"
                    onClick={[Function]}
                  >
                    <i
                      className="next-icon next-icon-arrow-down next-medium next-select-symbol-fold"
                      style={Object {}}
                    />
                  </span>
                </span>
              </span>
              <span
                aria-live="polite"
                className="next-sr-only"
              >
                
              </span>
            </span>
             
             
          </div>
        </div>
        <div
          __id="node_dockcy8n9xes"
          __inner__={
            Object {
              "condition": true,
              "hidden": undefined,
            }
          }
          className="next-form-item next-left next-medium"
          style={
            Object {
              "marginBottom": "0",
            }
          }
        >
          <div
            className="next-form-item-label"
          >
            <label>
              项目类型：
            </label>
          </div>
          <div
            className="next-form-item-control"
          >
            <span
              aria-haspopup={true}
              className="next-select next-select-trigger next-select-single next-medium next-inactive next-no-search"
              onClick={[Function]}
              onKeyDown={[Function]}
              onMouseDown={[Function]}
              onMouseEnter={[Function]}
              onMouseLeave={[Function]}
              style={
                Object {
                  "width": "200px",
                }
              }
            >
              <span
                className="next-input next-medium next-select-inner"
              >
                <span
                  className="next-select-values next-input-text-field"
                >
                  <span
                    className="next-select-trigger-search"
                  >
                    <input
                      __id="node_dockcy8n9xet"
                      __inner__={
                        Object {
                          "condition": true,
                          "hidden": undefined,
                        }
                      }
                      autoComplete="off"
                      disabled={false}
                      height="100%"
                      maxLength={null}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onCompositionEnd={[Function]}
                      onCompositionStart={[Function]}
                      onFocus={[Function]}
                      onKeyDown={[Function]}
                      placeholder="请选择"
                      readOnly={true}
                      role="combobox"
                      size="1"
                      tabIndex={0}
                      value=""
                    />
                    <span
                      aria-hidden={true}
                    >
                      <span>
                        请选择
                      </span>
                      <span
                        style={
                          Object {
                            "display": "inline-block",
                            "width": 1,
                          }
                        }
                      >
                         
                      </span>
                    </span>
                  </span>
                </span>
                <span
                  className="next-input-control"
                  onClick={[Function]}
                >
                  <span
                    aria-hidden={true}
                    className="next-select-arrow"
                    onClick={[Function]}
                  >
                    <i
                      className="next-icon next-icon-arrow-down next-medium next-select-symbol-fold"
                      style={Object {}}
                    />
                  </span>
                </span>
              </span>
              <span
                aria-live="polite"
                className="next-sr-only"
              >
                
              </span>
            </span>
             
             
          </div>
        </div>
        <div
          __id="node_dockcy8n9xeu"
          __inner__={
            Object {
              "condition": true,
              "hidden": undefined,
            }
          }
          className="next-form-item next-left next-medium"
          style={
            Object {
              "marginBottom": "0",
            }
          }
        >
          <div
            className="next-form-item-label"
          >
            <label>
              项目 ID：
            </label>
          </div>
          <div
            className="next-form-item-control"
          >
            <span
              className="next-input next-medium"
              style={
                Object {
                  "width": "200px",
                }
              }
            >
              <input
                __id="node_dockcy8n9xev"
                __inner__={
                  Object {
                    "condition": true,
                    "hidden": undefined,
                  }
                }
                autoComplete="off"
                disabled={false}
                height="100%"
                maxLength={null}
                onBlur={[Function]}
                onChange={[Function]}
                onCompositionEnd={[Function]}
                onCompositionStart={[Function]}
                onFocus={[Function]}
                onKeyDown={[Function]}
                readOnly={false}
                value=""
              />
            </span>
             
             
          </div>
        </div>
        <div
          __id="node_dockcy8n9xew"
          __inner__={
            Object {
              "condition": true,
              "hidden": undefined,
            }
          }
          className="next-btn-group"
        >
          <button
            __id="node_dockcy8n9xex"
            __inner__={
              Object {
                "condition": true,
                "hidden": undefined,
              }
            }
            className="next-btn next-medium next-btn-primary"
            disabled={false}
            onClick={[Function]}
            onMouseUp={[Function]}
            style={
              Object {
                "margin": "0 5px 0 5px",
              }
            }
            type="submit"
          >
            <span
              className="next-btn-helper"
            >
              搜索
            </span>
          </button>
          <button
            __id="node_dockcy8n9xe10"
            __inner__={
              Object {
                "condition": true,
                "hidden": undefined,
              }
            }
            className="next-btn next-medium next-btn-normal"
            disabled={false}
            onClick={[Function]}
            onMouseUp={[Function]}
            style={
              Object {
                "margin": "0 5px 0 5px",
              }
            }
            type="reset"
          >
            <span
              className="next-btn-helper"
            >
              清空
            </span>
          </button>
        </div>
      </form>
    </div>
    <div
      __id="node_dockcy8n9xe1f"
      __inner__={
        Object {
          "condition": true,
          "hidden": undefined,
        }
      }
      className="next-box"
      style={
        Object {
          "backgroundColor": "#ffffff",
          "display": "flex",
          "flexDirection": "row",
          "flexWrap": "nowrap",
          "justifyContent": "flex-end",
          "msFlexDirection": "column",
          "msFlexWrap": "none",
          "paddingBottom": "24px",
        }
      }
    >
      <button
        __events={
          Array [
            Object {
              "name": "onClick",
              "relatedEventName": "onClick",
              "type": "componentEvent",
            },
          ]
        }
        __id="node_dockd5nrh9p4"
        __inner__={
          Object {
            "condition": true,
            "hidden": undefined,
          }
        }
        className="next-btn next-medium next-btn-primary"
        disabled={false}
        onClick={[Function]}
        onMouseUp={[Function]}
        style={Object {}}
        type="button"
      >
        <span
          className="next-btn-helper"
        >
          新建配置
        </span>
      </button>
    </div>
    <div
      __id="node_dockd5nrh9p5"
      __inner__={
        Object {
          "condition": true,
          "hidden": undefined,
        }
      }
      className="next-box"
      style={
        Object {
          "flexDirection": "column",
          "flexWrap": "nowrap",
          "msFlexDirection": "column",
          "msFlexWrap": "none",
        }
      }
    >
      <div
        __id="node_dockjielosj1"
        __inner__={
          Object {
            "condition": true,
            "hidden": undefined,
          }
        }
        actionBar={
          Array [
            Object {
              "title": "新增",
              "type": "primary",
            },
            Object {
              "title": "编辑",
            },
          ]
        }
        actionColumn={
          Array [
            Object {
              "callback": [Function],
              "device": Array [
                "desktop",
              ],
              "title": "编辑",
            },
            Object {
              "callback": [Function],
              "mode": "EDIT",
              "title": "保存",
            },
          ]
        }
        actionFixed="right"
        actionHidden={false}
        actionTitle="操作"
        actionType="link"
        actionWidth={180}
        className="next-table next-table-medium"
        data={
          Array [
            Object {
              "age": 15000,
              "email": "<EMAIL>",
              "id": "1",
              "name": "王小",
            },
            Object {
              "age": 25000,
              "email": "<EMAIL>",
              "id": "2",
              "name": "王中",
            },
            Object {
              "age": 35000,
              "email": "<EMAIL>",
              "id": "3",
              "name": "王大",
            },
          ]
        }
        maxWebShownActionCount={2}
        showActionBar={true}
        showMiniPager={true}
        style={Object {}}
      >
        <div
          className="next-table-column-resize-proxy"
        />
        <table
          role="table"
          style={
            Object {
              "width": undefined,
            }
          }
        >
          <colgroup>
            <col
              style={
                Object {
                  "width": 200,
                }
              }
            />
            <col
              style={
                Object {
                  "width": 200,
                }
              }
            />
            <col
              style={
                Object {
                  "width": 200,
                }
              }
            />
          </colgroup>
          <thead
            className="next-table-header"
          >
            <tr>
              <th
                className="next-table-cell next-table-header-node"
                role="gridcell"
                rowSpan={1}
                style={
                  Object {
                    "textAlign": "center",
                  }
                }
              >
                <div
                  className="next-table-cell-wrapper"
                  data-next-table-col={0}
                >
                  姓名
                </div>
              </th>
              <th
                className="next-table-cell next-table-header-node"
                role="gridcell"
                rowSpan={1}
                style={
                  Object {
                    "textAlign": "center",
                  }
                }
              >
                <div
                  className="next-table-cell-wrapper"
                  data-next-table-col={1}
                >
                  年龄
                </div>
              </th>
              <th
                className="next-table-cell next-table-header-node"
                role="gridcell"
                rowSpan={1}
                style={
                  Object {
                    "textAlign": "center",
                  }
                }
              >
                <div
                  className="next-table-cell-wrapper"
                  data-next-table-col={2}
                >
                  邮箱
                </div>
              </th>
            </tr>
          </thead>
          <tbody
            className="next-table-body"
          >
            <tr>
              <td
                colSpan={3}
              >
                <div
                  className="next-table-empty"
                  style={
                    Object {
                      "left": 0,
                      "overflow": "hidden",
                      "position": "sticky",
                      "width": -1,
                    }
                  }
                >
                  没有数据
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        __id="node_dockd5nrh9pg"
        __inner__={
          Object {
            "condition": true,
            "hidden": undefined,
          }
        }
        className="next-box"
        style={
          Object {
            "display": "flex",
            "flexDirection": "row",
            "flexWrap": "nowrap",
            "justifyContent": "flex-end",
            "msFlexDirection": "column",
            "msFlexWrap": "none",
          }
        }
      >
        <div
          __id="node_dockd5nrh9pf"
          __inner__={
            Object {
              "condition": true,
              "hidden": undefined,
            }
          }
          className="next-pagination next-medium next-normal"
          style={Object {}}
        >
          <div
            className="next-pagination-pages"
          >
            <button
              aria-label="上一页，当前第1页"
              className="next-btn next-medium next-btn-normal next-pagination-item next-prev"
              disabled={true}
              onClick={[Function]}
              onMouseUp={[Function]}
              type="button"
            >
              <i
                className="next-icon next-icon-arrow-left next-xs next-btn-icon next-icon-first next-pagination-icon-prev"
                style={Object {}}
              />
              <span
                className="next-btn-helper"
              >
                上一页
              </span>
            </button>
            <div
              className="next-pagination-list"
            >
              <button
                aria-label="第1页，共10页"
                className="next-btn next-medium next-btn-normal next-pagination-item next-current"
                disabled={false}
                onClick={[Function]}
                onMouseUp={[Function]}
                type="button"
              >
                <span
                  className="next-btn-helper"
                >
                  1
                </span>
              </button>
              <button
                aria-label="第2页，共10页"
                className="next-btn next-medium next-btn-normal next-pagination-item"
                disabled={false}
                onClick={[Function]}
                onMouseUp={[Function]}
                type="button"
              >
                <span
                  className="next-btn-helper"
                >
                  2
                </span>
              </button>
              <button
                aria-label="第3页，共10页"
                className="next-btn next-medium next-btn-normal next-pagination-item"
                disabled={false}
                onClick={[Function]}
                onMouseUp={[Function]}
                type="button"
              >
                <span
                  className="next-btn-helper"
                >
                  3
                </span>
              </button>
              <button
                aria-label="第4页，共10页"
                className="next-btn next-medium next-btn-normal next-pagination-item"
                disabled={false}
                onClick={[Function]}
                onMouseUp={[Function]}
                type="button"
              >
                <span
                  className="next-btn-helper"
                >
                  4
                </span>
              </button>
              <i
                className="next-icon next-icon-ellipsis next-medium next-pagination-ellipsis next-pagination-icon-ellipsis"
                style={Object {}}
              />
              <button
                aria-label="第10页，共10页"
                className="next-btn next-medium next-btn-normal next-pagination-item"
                disabled={false}
                onClick={[Function]}
                onMouseUp={[Function]}
                type="button"
              >
                <span
                  className="next-btn-helper"
                >
                  10
                </span>
              </button>
            </div>
            <button
              aria-label="下一页，当前第1页"
              className="next-btn next-medium next-btn-normal next-pagination-item next-next"
              disabled={false}
              onClick={[Function]}
              onMouseUp={[Function]}
              type="button"
            >
              <span
                className="next-btn-helper"
              >
                下一页
              </span>
              <i
                className="next-icon next-icon-arrow-right next-xs next-btn-icon next-icon-last next-pagination-icon-next"
                style={Object {}}
              />
            </button>
            <span
              className="next-pagination-display"
            >
              <em>
                1
              </em>
              /
              10
            </span>
            <span
              className="next-pagination-jump-text"
            >
              到第
            </span>
            <span
              className="next-input next-medium next-pagination-jump-input"
            >
              <input
                aria-label="请输入跳转到第几页"
                autoComplete="off"
                disabled={false}
                height="100%"
                maxLength={null}
                onBlur={[Function]}
                onChange={[Function]}
                onCompositionEnd={[Function]}
                onCompositionStart={[Function]}
                onFocus={[Function]}
                onKeyDown={[Function]}
                readOnly={false}
                value=""
              />
            </span>
            <span
              className="next-pagination-jump-text"
            >
              页
            </span>
            <button
              className="next-btn next-medium next-btn-normal next-pagination-jump-go"
              disabled={false}
              onClick={[Function]}
              onMouseUp={[Function]}
              type="button"
            >
              <span
                className="next-btn-helper"
              >
                确定
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <span
    aria-haspopup={true}
    className="next-select next-select-trigger next-select-single next-medium next-inactive next-no-search"
    onClick={[Function]}
    onKeyDown={[Function]}
    onMouseDown={[Function]}
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
  >
    <span
      className="next-input next-medium next-select-inner"
    >
      <span
        className="next-select-values next-input-text-field"
      >
        <span
          className="next-select-trigger-search"
        >
          <input
            __id="node_dockd5nrh9pr"
            __inner__={
              Object {
                "condition": true,
                "hidden": undefined,
              }
            }
            autoComplete="off"
            disabled={false}
            height="100%"
            maxLength={null}
            name="error"
            onBlur={[Function]}
            onChange={[Function]}
            onCompositionEnd={[Function]}
            onCompositionStart={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="请选择"
            readOnly={true}
            role="combobox"
            size="1"
            tabIndex={0}
            value=""
          />
          <span
            aria-hidden={true}
          >
            <span>
              请选择
            </span>
            <span
              style={
                Object {
                  "display": "inline-block",
                  "width": 1,
                }
              }
            >
               
            </span>
          </span>
        </span>
      </span>
      <span
        className="next-input-control"
        onClick={[Function]}
      >
        <span
          aria-hidden={true}
          className="next-select-arrow"
          onClick={[Function]}
        >
          <i
            className="next-icon next-icon-arrow-down next-medium next-select-symbol-fold"
            style={Object {}}
          />
        </span>
      </span>
    </span>
    <span
      aria-live="polite"
      className="next-sr-only"
    >
      
    </span>
  </span>
</div>
`;

exports[`JSExpression JSExpression props 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    visible={true}
  />
</div>
`;

exports[`JSExpression JSExpression props with loop 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    name1="1"
    name2="1"
  />
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    name1="2"
    name2="2"
  />
</div>
`;

exports[`JSExpression JSExpression props with loop, and thisRequiredInJSE is true 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    name1="1"
  />
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    name1="2"
  />
</div>
`;

exports[`JSExpression JSFunction props 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    onClick={[Function]}
  />
</div>
`;

exports[`JSExpression JSSlot has loop 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __id="node_ocl1ao1o7w3"
    __inner__={
      Object {
        "condition": true,
        "hidden": false,
      }
    }
    __style__=":root {
 padding: 12px;
 background: #f2f2f2;
 border: 1px solid #ddd;
}"
    behavior="NORMAL"
    className="div_l1ao7pfc"
    customClassName=""
    fieldId="div_l1ao7lvq"
    forwardRef={[Function]}
    useFieldIdAsDomId={false}
  >
    <div
      __id="node_ocl1ao1o7w4"
      __inner__={
        Object {
          "condition": true,
          "hidden": false,
        }
      }
      __style__=":root {
 font-size: 14px;
 color: #666;
}"
      behavior="NORMAL"
      className="text_l1ao7pfb"
      content="这是一个低代码业务组件~"
      fieldId="text_l1ao7lvp"
      forwardRef={[Function]}
      maxLine={0}
      showTitle={false}
    >
      这是一个低代码业务组件~
    </div>
  </div>
  <div
    __id="node_ocl1ao1o7w3"
    __inner__={
      Object {
        "condition": true,
        "hidden": false,
      }
    }
    __style__=":root {
 padding: 12px;
 background: #f2f2f2;
 border: 1px solid #ddd;
}"
    behavior="NORMAL"
    className="div_l1ao7pfc"
    customClassName=""
    fieldId="div_l1ao7lvq"
    forwardRef={[Function]}
    useFieldIdAsDomId={false}
  >
    <div
      __id="node_ocl1ao1o7w4"
      __inner__={
        Object {
          "condition": true,
          "hidden": false,
        }
      }
      __style__=":root {
 font-size: 14px;
 color: #666;
}"
      behavior="NORMAL"
      className="text_l1ao7pfb"
      content="这是一个低代码业务组件~"
      fieldId="text_l1ao7lvp"
      forwardRef={[Function]}
      maxLine={0}
      showTitle={false}
    >
      这是一个低代码业务组件~
    </div>
  </div>
  <div
    __id="node_ocl1ao1o7w3"
    __inner__={
      Object {
        "condition": true,
        "hidden": false,
      }
    }
    __style__=":root {
 padding: 12px;
 background: #f2f2f2;
 border: 1px solid #ddd;
}"
    behavior="NORMAL"
    className="div_l1ao7pfc"
    customClassName=""
    fieldId="div_l1ao7lvq"
    forwardRef={[Function]}
    useFieldIdAsDomId={false}
  >
    <div
      __id="node_ocl1ao1o7w4"
      __inner__={
        Object {
          "condition": true,
          "hidden": false,
        }
      }
      __style__=":root {
 font-size: 14px;
 color: #666;
}"
      behavior="NORMAL"
      className="text_l1ao7pfb"
      content="这是一个低代码业务组件~"
      fieldId="text_l1ao7lvp"
      forwardRef={[Function]}
      maxLine={0}
      showTitle={false}
    >
      这是一个低代码业务组件~
    </div>
  </div>
</div>
`;

exports[`JSExpression base props 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
    text="123"
    visible={true}
  />
</div>
`;

exports[`designMode designMode:default 1`] = `
<div
  className="lce-page"
  style={Object {}}
>
  <div
    __inner__={
      Object {
        "condition": true,
        "hidden": undefined,
      }
    }
    className="div-ut"
    forwardRef={[Function]}
  >
    <div
      __inner__={
        Object {
          "condition": true,
          "hidden": undefined,
        }
      }
      className="div-ut-children"
      forwardRef={[Function]}
    />
  </div>
</div>
`;
