// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`children this.props.children is array 1`] = `
<div>
  <div
    content="content"
  >
    content
  </div>
  <div
    content="content"
  >
    content
  </div>
</div>
`;

exports[`lifecycle leaf change and make componentWillReceiveProps 1`] = `
<div>
  <div
    __id="text6"
    __tag="222"
    componentId="text6"
    content="content new leaf"
  >
    content new leaf
  </div>
</div>
`;

exports[`lifecycle props change and make componentWillReceiveProps 1`] = `
<div>
  <div
    content="content"
  >
    content
  </div>
</div>
`;

exports[`lifecycle props change and make componentWillReceiveProps 2`] = `
<div>
  <div
    content="content 123"
  >
    content 123
  </div>
</div>
`;

exports[`lifecycle props change and make componentWillReceiveProps 3`] = `
<div>
  <div
    __tag="111"
    content="content 123"
  >
    content 123
  </div>
</div>
`;

exports[`mini unit render leaf has a loop, render from parent 1`] = `
<div>
  this is a new children
</div>
`;

exports[`mini unit render make text props change 1`] = `
<div>
  <div
    content="content"
  >
    content
  </div>
</div>
`;

exports[`mini unit render make text props change 2`] = `
<div
  newPropKey="newPropValue"
/>
`;

exports[`mini unit render parent is a mock leaf 1`] = `
<div>
  <div
    content="new content to mock"
  >
    new content to mock
  </div>
</div>
`;

exports[`mini unit render props has new children 1`] = `
<div>
  children 01
  children 02
</div>
`;

exports[`onChildrenChange children is array string 1`] = `
<div>
  onChildrenChange content 01
  onChildrenChange content 02
</div>
`;

exports[`onPropChange change textNode [key:___condition___] props, but not hidden component 1`] = `
<div>
  <div
    content="content"
  >
    content
  </div>
</div>
`;

exports[`onPropChange change textNode [key:___condition___] props, hide textNode component 1`] = `<div />`;

exports[`onPropChange change textNode [key:content], content in this.props but not in leaf.export result 1`] = `
<div>
  <div
    content="content"
  >
    content
  </div>
</div>
`;

exports[`onPropChange change textNode [key:content], content in this.props but not in leaf.export result 2`] = `
<div>
  <div
    content={null}
  />
</div>
`;

exports[`onVisibleChange visible is false 1`] = `<div />`;

exports[`onVisibleChange visible is true 1`] = `
<div>
  <div
    content="content"
  >
    content
  </div>
</div>
`;
