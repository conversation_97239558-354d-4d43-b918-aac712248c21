{"name": "@alilc/lowcode-workspace", "version": "1.3.2", "description": "Shell Layer for AliLowCodeEngine", "main": "lib/index.js", "module": "es/index.js", "files": ["lib", "es"], "scripts": {"build": "build-scripts build", "test": "build-scripts test --config build.test.json", "test:cov": "build-scripts test --config build.test.json --jest-coverage"}, "license": "MIT", "dependencies": {"@alilc/lowcode-designer": "1.3.2", "@alilc/lowcode-editor-core": "1.3.2", "@alilc/lowcode-editor-skeleton": "1.3.2", "@alilc/lowcode-types": "1.3.2", "@alilc/lowcode-utils": "1.3.2", "classnames": "^2.2.6", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.5", "react": "^16", "react-dom": "^16.7.0"}, "devDependencies": {"@alib/build-scripts": "^0.1.29", "@testing-library/react": "^11.2.2", "@types/classnames": "^2.2.7", "@types/jest": "^26.0.16", "@types/lodash": "^4.14.165", "@types/medium-editor": "^5.0.3", "@types/node": "^13.7.1", "@types/react": "^16", "@types/react-dom": "^16", "jest": "^26.6.3", "lodash": "^4.17.20", "moment": "^2.29.1", "typescript": "^4.0.3"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "http", "url": "https://github.com/alibaba/lowcode-engine/tree/main/packages/workspace"}, "gitHead": "2669f179e6f899d395ce1942d0fe04f9c5ed48a6", "bugs": "https://github.com/alibaba/lowcode-engine/issues", "homepage": "https://github.com/alibaba/lowcode-engine/#readme"}