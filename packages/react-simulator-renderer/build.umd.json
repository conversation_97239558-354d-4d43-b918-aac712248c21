{"entry": {"react-simulator-renderer": "src/index"}, "sourceMap": true, "library": "___ReactSimulator<PERSON><PERSON><PERSON>___", "libraryTarget": "umd", "externals": {"react": "var window.React", "react-dom": "var window.ReactDOM", "prop-types": "var window.PropTypes", "@alifd/next": "var Next", "@alilc/lowcode-engine-ext": "var window.AliLowCodeEngineExt", "moment": "var moment", "lodash": "var _"}, "polyfill": false, "outputDir": "dist", "vendor": false, "ignoreHtmlTemplate": true, "plugins": ["build-plugin-react-app", ["build-plugin-fusion", {"externalNext": "umd"}], ["build-plugin-moment-locales", {"locales": ["zh-cn"]}], "./build.plugin.js"]}