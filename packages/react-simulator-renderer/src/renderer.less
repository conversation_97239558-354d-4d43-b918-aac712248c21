body, html {
  display: block;
  background: white;
  padding: 0;
  margin: 0;
}

html.engine-design-mode {
  padding-bottom: 0;
}

html.engine-cursor-move, html.engine-cursor-move * {
  cursor: grabbing !important;
}

html.engine-cursor-copy, html.engine-cursor-copy * {
  cursor: copy !important;
}

html.engine-cursor-ew-resize, html.engine-cursor-ew-resize * {
  cursor: ew-resize !important;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
}

.lc-container {
  &:empty {
    background: #f2f3f5;
    color: #a7b1bd;
    outline: 1px dashed rgba(31, 56, 88, 0.2);
    outline-offset: -1px !important;
    height: 66px;
    max-height: 100%;
    min-width: 140px;
    text-align: center;
    overflow: hidden;
    display: flex;
    align-items: center;
    &:before {
      content: '\62D6\62FD\7EC4\4EF6\6216\6A21\677F\5230\8FD9\91CC';
      font-size: 14px;
      z-index: 1;
      width: 100%;
      white-space: nowrap;
    }
  }
}

.lc-container-placeholder {
  min-height: 60px;
  height: 100%;
  width: 100%;
  background-color: rgb(240, 240, 240);
  border: 1px dotted;
  color: rgb(167, 177, 189);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  &.lc-container-locked {
    background: #eccfcf;
  }
}

body.engine-document {
  &:after, &:before {
    content: "";
    display: table;
  }
  &:after {
    clear: both;
  }
}

.engine-live-editing {
  cursor: text;
  outline: none;
  box-shadow: 0 0 0 2px rgb(102, 188, 92);
  user-select: text;
}

#app {
  height: 100vh;
}


