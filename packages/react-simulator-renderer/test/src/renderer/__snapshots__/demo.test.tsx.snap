// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Base should be render NotFoundComponent 1`] = `
<div
  className="lce-page page_kvuu9hym"
  style={Object {}}
>
  <div
    componentName="Div"
  >
    <div
      componentName="Text"
    >
      Text Component Not Found
    </div>
  </div>
</div>
`;

exports[`Base should be render Text 1`] = `
<div
  className="lce-page page_kvuu9hym"
  style={Object {}}
>
  <div
    componentName="Div"
  >
    <div
      __designMode="design"
      __style__={Object {}}
      behavior="NORMAL"
      componentId="node_ockvuu8u916"
      fieldId="text_kvuu9gl2"
      forwardRef={[Function]}
      maxLine={0}
      showTitle={false}
    >
      我是一个简单的测试页面
    </div>
  </div>
</div>
`;
