{"name": "@alilc/lowcode-editor-core", "version": "1.3.2", "description": "Core Api for Ali lowCode engine", "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "files": ["lib", "es"], "scripts": {"build": "build-scripts build", "test": "build-scripts test --config build.test.json", "test:cov": "build-scripts test --config build.test.json --jest-coverage"}, "dependencies": {"@alifd/next": "^1.19.16", "@alilc/lowcode-types": "1.3.2", "@alilc/lowcode-utils": "1.3.2", "classnames": "^2.2.6", "debug": "^4.1.1", "intl-messageformat": "^9.3.1", "lodash.get": "^4.4.2", "mobx": "^6.3.0", "mobx-react": "^7.2.0", "power-di": "^2.2.4", "react": "^16", "react-dom": "^16.7.0", "store": "^2.0.12"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@alilc/lowcode-datasource-types": "^1.0.1", "@types/classnames": "^2.2.7", "@types/lodash.get": "^4.4.6", "@types/node": "^13.7.1", "@types/react": "^16", "@types/react-dom": "^16", "@types/store": "^2.0.2", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "http", "url": "https://github.com/alibaba/lowcode-engine/tree/main/packages/editor-core"}, "gitHead": "2669f179e6f899d395ce1942d0fe04f9c5ed48a6", "bugs": "https://github.com/alibaba/lowcode-engine/issues", "homepage": "https://github.com/alibaba/lowcode-engine/#readme"}