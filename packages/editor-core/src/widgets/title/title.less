.lc-title {
  display: inline-flex;
  align-items: center;
  color: var(--color-text);
  .lc-title-icon {
    display: flex;
    align-items: center;
    margin-right: 4px;
    img {
      width: 16px;
      height: 16px;
      filter: brightness(0) invert(1);
    }
  }
  &.only-icon {
    .lc-title-icon {
      margin-right: 0;
    }
  }
  &.has-tip {
    cursor: help;
    text-decoration-line: underline;
    text-decoration-style: dashed;
    text-decoration-color: var(--color-text-light, rgba(31, 56, 88, .3));
  }
  line-height: initial !important;
  word-break: break-all;
}

.actived .lc-title {
  color: var(--color-actived);
}
