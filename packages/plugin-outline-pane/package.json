{"name": "@alilc/lowcode-plugin-outline-pane", "version": "1.3.2", "description": "Outline pane for Ali lowCode engine", "files": ["es", "lib"], "main": "lib/index.js", "module": "es/index.js", "scripts": {"build": "build-scripts build"}, "dependencies": {"@alifd/next": "^1.19.16", "@alilc/lowcode-types": "1.3.2", "@alilc/lowcode-utils": "1.3.2", "classnames": "^2.2.6", "react": "^16", "react-dom": "^16.7.0", "ric-shim": "^1.0.1"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@types/classnames": "^2.2.7", "@types/node": "^13.7.1", "@types/react": "^16", "@types/react-dom": "^16", "build-plugin-fusion": "^0.1.1", "build-plugin-moment-locales": "^0.1.0"}, "license": "MIT", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "http", "url": "https://github.com/alibaba/lowcode-engine/tree/main/packages/plugin-outline-pane"}, "gitHead": "2669f179e6f899d395ce1942d0fe04f9c5ed48a6", "bugs": "https://github.com/alibaba/lowcode-engine/issues", "homepage": "https://github.com/alibaba/lowcode-engine/#readme"}