.lc-outline-pane {
  height: 100%;
  width: 100%;
  position: relative;
  z-index: 200;

  > .lc-outline-tree-container {
    top: 52px;
    left: 0;
    bottom: 0;
    right: 0;
    position: absolute;
    overflow: auto;
  }

  > .lc-outline-tree-container.lc-hidden-outline-filter {
    top: 0;
  }

  > .lc-outline-filter {
    padding: 12px 16px;
    display: flex;
    align-items: stretch;
    justify-content: right;

    .lc-outline-filter-search-input {
      width: 100%;
    }

    .lc-outline-filter-icon {
      background: var(--color-block-background-light, #ebecf0);
      border: 1px solid var(--color-field-border, #c4c6cf);
      display: flex;
      align-items: center;
      border-radius: 0 2px 2px 0;
      overflow: hidden;
      margin-left: -2px;
      z-index: 1;
      padding: 0 6px;

      &:hover {
        cursor: pointer;
      }
    }
  }
}

.lc-outline-tree {
  @treeNodeHeight: 30px;
  overflow: hidden;
  margin-bottom: @treeNodeHeight;
  user-select: none;
  overflow-x: scroll;

  .tree-node-modal {
    margin: 5px;
    border: 1px solid var(--color-field-border, rgba(31, 56, 88, 0.2));
    border-radius: 3px;
    box-shadow: 0 1px 4px 0 var(--color-block-background-shallow, rgba(31, 56, 88, 0.15));

    .tree-node-modal-title {
      position: relative;
      background: var(--color-block-background-light, rgba(31, 56, 88, 0.04));
      padding: 0 10px;
      height: 32px;
      line-height: 32px;
      border-bottom: 1px solid var(--color-field-border, rgba(31, 56, 88, 0.2));

      .tree-node-modal-title-visible-icon {
        position: absolute;
        top: 4px;
        right: 12px;
        cursor: pointer;
      }
    }

    .tree-pane-modal-content {
      & > .tree-node-branches::before {
        display: none;
      }
    }

    .tree-node-modal-radio,
    .tree-node-modal-radio-active {
      margin-right: 4px;
      opacity: 0.8;
      position: absolute;
      top: 7px;
      left: 6px;
    }
    .tree-node-modal-radio-active {
      color: var(--color-brand, #006cff);
    }
  }

  .tree-node-branches::before {
    position: absolute;
    display: block;
    width: 0;
    border-left: 1px solid transparent;
    height: 100%;
    top: 0;
    left: 6px;
    content: ' ';
    z-index: 2;
    pointer-events: none;
  }

  &:hover {
    .tree-node-branches::before {
      border-left-color: var(--color-line-darken, #ddd);
    }
  }

  .insertion {
    pointer-events: all !important;
    border: 1px dashed var(--color-brand-light);
    height: @treeNodeHeight;
    box-sizing: border-box;
    transform: translateZ(0);
    transition: all 0.2s ease-in-out;
    &.invalid {
      border-color: var(--color-error, var(--color-function-error, red));
      background-color: var(--color-block-background-error, rgba(240, 154, 154, 0.719));
    }
  }

  .condition-group-container {
    border-bottom: 1px solid var(--color-brown, var(--color-function-brown, #7b605b));
    position: relative;

    &:before {
      position: absolute;
      display: block;
      width: 0;
      border-left: 0.5px solid var(--color-brown, var(--color-function-brown, #7b605b));
      height: 100%;
      top: 0;
      left: 0;
      content: ' ';
      z-index: 2;
    }
    > .condition-group-title {
      text-align: center;
      background-color: var(--color-brown, var(--color-function-brown, #7b605b));
      height: 14px;
      > .lc-title {
        font-size: 12px;
        transform: scale(0.8);
        transform-origin: top;
        color: var(--color-text-reverse, white);
        text-shadow: 0 0 2px var(--color-block-background-shallow, black);
        display: block;
      }
    }
  }
  .tree-node-slots {
    border-bottom: 1px solid var(--color-purple, var(--color-function-purple, rgb(144, 94, 190)));
    position: relative;
    &::before {
      position: absolute;
      display: block;
      width: 0;
      border-left: 0.5px solid var(--color-purple, var(--color-function-purple, rgb(144, 94, 190)));
      height: 100%;
      top: 0;
      left: 0;
      content: ' ';
      z-index: 2;
    }
    > .tree-node-slots-title {
      text-align: center;
      background-color: var(--color-purple, var(--color-function-purple, rgb(144, 94, 190)));
      height: 14px;
      > .lc-title {
        font-size: 12px;
        transform: scale(0.8);
        transform-origin: top;
        color: var(--color-text-reverse, white);
        text-shadow: 0 0 2px black;
        display: block;
      }
    }
    &.insertion-at-slots {
      padding-bottom: @treeNodeHeight;
      border-bottom-color: var(--color-error-dark, var(--color-function-error-dark, rgb(182, 55, 55)));
      > .tree-node-slots-title {
        background-color: var(--color-error-dark, var(--color-function-error-dark, rgb(182, 55, 55)));
      }
      &::before {
        border-left-color: var(--color-error-dark, var(--color-function-error-dark, rgb(182, 55, 55)));
      }
    }
  }

  .tree-node {
    .tree-node-expand-btn {
      width: 12px;
      line-height: 0;
      align-self: stretch;
      display: flex;
      align-items: center;
      transition: color 200ms ease;
      color: var(--color-icon-normal);
      &:hover {
        color: var(--color-icon-hover);
      }
      > svg {
        transform-origin: center;
        transform: rotate(-90deg);
        transition: transform 100ms ease;
      }
      margin-right: 4px;
    }
    .tree-node-expand-placeholder {
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }

    .tree-node-icon {
      transform: translateZ(0);
      display: flex;
      align-items: center;
      margin-right: 4px;
      color: var(--color-text);

      & > svg {
        width: 16px;
        height: 16px;
        * {
          fill: var(--color-icon-normal, rgba(31, 56, 88, 0.4));
        }
      }
      & > img {
        width: 16px;
        height: 16px;
        * {
          fill: var(--color-icon-normal, rgba(31, 56, 88, 0.4));
        }
      }
    }

    .tree-node-title {
      font-size: var(--font-size-text);
      cursor: pointer;
      border-bottom: 1px solid var(--color-line-normal, rgba(31, 56, 88, 0.1));
      display: flex;
      align-items: center;
      height: @treeNodeHeight;
      box-sizing: border-box;
      position: relative;
      transform: translateZ(0);
      padding-right: 5px;
      & > :first-child {
        margin-left: 2px;
      }

      .tree-node-title-label {
        flex: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        align-self: stretch;
        overflow: visible;
        margin-right: 5px;

        .tree-node-title-input {
          flex: 1;
          border: 1px solid var(--color-brand-light);
          background-color: var(--color-pane-background);
          color: var(--color-text);
          line-height: 18px;
          padding: 2px;
          outline: none;
          margin-left: -3px;
          border-radius: 2px;
        }
      }

      .tree-node-hide-btn,
      .tree-node-lock-btn,
      .tree-node-rename-btn,
      .tree-node-delete-btn {
        opacity: 0;
        color: var(--color-text);
        line-height: 0;
        align-self: stretch;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        &:hover {
          opacity: 1 !important;
        }
      }
      &:hover {
        .tree-node-hide-btn,
        .tree-node-lock-btn,
        .tree-node-rename-btn,
        .tree-node-delete-btn {
          opacity: 0.5;
        }
      }
      html.lc-cursor-dragging & {
        // FIXME: only hide hover shows
        .tree-node-hide-btn,
        .tree-node-lock-btn,
        .tree-node-rename-btn {
          display: none;
        }
      }
      &.editing {
        & > .tree-node-hide-btn,
        & > .tree-node-lock-btn,
        & > .tree-node-rename-btn,
        & > .tree-node-delete-btn {
          display: none;
        }
      }

      .tree-node-tag {
        margin-left: 5px;
        display: flex;
        align-items: center;
        line-height: 0;
        &.cond {
          color: var(--color-error, var(--color-function-error, rgb(179, 52, 6)));
        }
        &.loop {
          color: var(--color-success, var(--color-function-success, rgb(103, 187, 187)));
        }
        &.slot {
          color: var(--color-purple, var(--color-function-purple, rgb(211, 90, 211)));
        }
      }
    }

    &.is-root {
      > .tree-node-title {
        padding-left: 5px;
      }
    }

    &.expanded {
      & > .tree-node-title > .tree-node-expand-btn > svg {
        transform: rotate(0);
      }
    }

    &.detecting > .tree-node-title {
      background: var(--color-block-background-light);
    }

    // 选中节点处理
    &.selected {
      & > .tree-node-title {
        background: var(--color-block-background-light);
      }

      & > .tree-node-branches::before {
        border-left-color: var(--color-brand-light);
      }
    }

    &.hidden {
      .tree-node-title-label {
        color: var(--color-text-disabled, #9b9b9b);
      }
      & > .tree-node-title > .tree-node-hide-btn {
        opacity: 0.8;
      }
      .tree-node-branches {
        .tree-node-hide-btn {
          opacity: 0;
        }
      }
    }

    &.condition-flow {
      & > .tree-node-title > .tree-node-hide-btn {
        opacity: 1;
      }
      &.hidden > .tree-node-title > .tree-node-hide-btn {
        opacity: 0;
      }
    }

    &.locked {
      & > .tree-node-title > .tree-node-lock-btn {
        opacity: 0.8;
      }
      .tree-node-branches {
        .tree-node-lock-btn,
        .tree-node-hide-btn {
          opacity: 0;
        }
      }
    }

    // 处理拖入节点
    &.dropping {
      & > .tree-node-branches::before {
        border-left: 1px solid var(--color-brand);
      }
      & > .tree-node-title {
        .tree-node-expand-btn {
          color: var(--color-brand);
        }
        .tree-node-icon {
          color: var(--color-brand);
        }
        .tree-node-title-label > .lc-title {
          color: var(--color-brand);
        }
      }
    }
    &.highlight {
      & > .tree-node-title {
        background: var(--color-block-background-shallow);
      }
    }

    .tree-node-branches {
      padding-left: 12px;
      position: relative;
    }
  }
}
