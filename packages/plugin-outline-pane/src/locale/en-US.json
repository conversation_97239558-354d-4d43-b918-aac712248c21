{"Initializing": "Initializing", "Hide": "<PERSON>de", "Show": "Show", "Lock": "Lock", "Unlock": "Unlock", "Expand": "Expand", "Collapse": "Collapse", "Conditional": "Condition", "Loop": "Loop", "Slots": "Slots", "Slot for {prop}": "Slot for {prop}", "Outline Tree": "Component Tree", "Filter Node": "<PERSON><PERSON>", "Check All": "Check All", "Conditional rendering": "Conditional rendering", "Loop rendering": "Loop rendering", "Locked": "Locked", "Hidden": "Hidden", "Modal View": "Modal View", "Rename": "<PERSON><PERSON>", "Delete": "Delete"}