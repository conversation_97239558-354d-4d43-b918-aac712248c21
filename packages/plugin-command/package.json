{"name": "@alilc/lowcode-plugin-command", "version": "1.3.2", "description": "> TODO: description", "author": "liujuping <<EMAIL>>", "homepage": "https://github.com/alibaba/lowcode-engine#readme", "license": "ISC", "main": "lib/index.js", "module": "es/index.js", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib", "es"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/alibaba/lowcode-engine.git"}, "scripts": {"test": "build-scripts test --config build.test.json --jest-passWithNoTests", "build": "build-scripts build"}, "bugs": {"url": "https://github.com/alibaba/lowcode-engine/issues"}, "dependencies": {"@alilc/lowcode-types": "1.3.2", "@alilc/lowcode-utils": "1.3.2"}, "devDependencies": {"@alib/build-scripts": "^0.1.18"}}