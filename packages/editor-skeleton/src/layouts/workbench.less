@import './theme.less';

:root {
  --font-family: @font-family;
  --font-size-label: @fontSize-4;
  --font-size-text: @fontSize-5;
  --font-size-btn-large: @fontSize-3;
  --font-size-btn-medium: @fontSize-4;
  --font-size-btn-small: @fontSize-5;

  --global-border-radius: @global-border-radius;
  --input-border-radius: @input-border-radius;
  --popup-border-radius: @popup-border-radius;

  --left-area-width: 48px;
  --workspace-left-area-width: 48px;
  --right-area-width: 300px;
  --top-area-height: 48px;
  --toolbar-height: 36px;
  --dock-pane-width: 300px;
  --dock-fixed-pane-width: 300px;
}

@media (min-width: 1860px) {
  :root {
    --right-area-width: 400px;
    --dock-pane-width: 452px;
    --dock-fixed-pane-width: 350px;
  }
}

html,
body {
  height: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
  position: relative;
  font-family: var(--font-family);
  font-size: var(--font-size-text);
  color: var(--color-text);
  background-color: var(--color-background);
}

* {
  box-sizing: border-box;
}

.lc-titled-panel {
  width: 100%;
  height: 100%;
  position: relative;
  &.hidden {
    display: none;
  }
  .lc-panel-title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 15px;

    .lc-help-tip {
      margin-left: 4px;
      color: var(--color-icon-normal, rgba(0, 0, 0, 0.4));
      cursor: pointer;
    }
  }
  > .lc-panel-title {
    height: var(--pane-title-height, 48px);
    font-size: var(--pane-title-font-size, 16px);
    padding: var(--pane-title-padding, 0 15px);
    color: var(--color-title, #0f1726);
    font-weight: bold;
  }

  .lc-panel-body {
    position: absolute;
    top: var(--pane-title-height, 48px);
    bottom: 0;
    left: 0;
    right: 0;
    overflow: visible;
  }
  .lc-outline-tree-container {
    border-top: 1px solid var(--color-line-normal, rgba(31, 56, 88, 0.1));
  }
}
.lc-panel {
  height: 100%;
  width: 100%;
  position: relative;
  &.hidden {
    display: none;
  }
}

.workspace-engine-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;

  &.active {
    z-index: 999;
  }

  .lc-workbench {

  }

  .engine-editor-view {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;

    &.active {
      z-index: 999;
      background: var(--color-background);
    }
  }
}

.lc-workbench, .lc-workspace-workbench {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);

  &.engine-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--color-background);
  }
  .lc-top-area, .lc-workspace-sub-top-area {
    width: 100%;
    display: none;
    margin-bottom: 2px;
    padding: 8px 12px 8px 16px;

    &.lc-top-area {
      background-color: var(--color-top-area-background, var(--color-pane-background));
      height: var(--top-area-height);
    }

    &.lc-workspace-top-area {
      background-color: var(--color-workspace-top-area-background, var(--color-pane-background));
    }

    &.lc-workspace-sub-top-area {
      background-color: var(--color-workspace-sub-top-area-background, var(--color-pane-background));
      height: var(--workspace-sub-top-area-height, var(--top-area-height));
      margin: var(--workspace-sub-top-area-margin, 0px 0px 2px 0px);
      padding: var(--workspace-sub-top-area-padding, 8px 12px 8px 16px);
    }

    &.lc-area-visible {
      display: flex;
    }

    .lc-top-area-left, .lc-workspace-sub-top-area-left {
      display: flex;
      align-items: center;
      max-width: 100%;
    }

    .lc-top-area-center, .lc-workspace-sub-top-area-center {
      flex: 1;
      display: flex;
      justify-content: center;
      margin: 0 8px;
    }
    .lc-top-area-right, .lc-workspace-sub-top-area-right {
      display: flex;
      align-items: center;
      > * {
        margin-left: 4px;
        margin-right: 4px;
      }
      .ve-quick-search-trigger {
        display: flex;
      }
    }
  }
  .lc-workbench-body, .lc-workspace-workbench-body {
    flex: 1;
    display: flex;
    min-height: 0;
    position: relative;

    .lc-tabs-title {
      width: 100%;
      height: 32px;
      position: relative;
      display: center;
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom: 1px solid var(--color-line-normal, #edeff3);
      .lc-tab-title {
        flex: 1;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 2px solid transparent;
        cursor: pointer;
        font-size: 12px;
        &.actived {
          color: var(--color-brand, #0079f2);
          border-bottom-color: var(--color-brand, #0079f2);
        }
      }
    }

    .lc-tabs-content {
      position: absolute;
      top: 32px;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .lc-pane-icon-close {
      position: absolute;
      right: 16px;
      top: calc(var(--pane-title-height, 48px) / 2 - 10px);
      height: auto;
      z-index: 2;
      .next-icon {
        line-height: 1;
        color: var(--color-icon-pane);
      }
    }

    .lc-pane-icon-fix,
    .lc-pane-icon-float {
      position: absolute;
      right: 38px;
      top: calc(var(--pane-title-height, 48px) / 2 - 10px);
      height: auto;
      z-index: 2;
      svg {
        vertical-align: middle;
        color: var(--color-icon-pane);
      }
    }

    .lc-left-float-pane {
      position: absolute;
      top: 0;
      bottom: 0;
      width: var(--dock-pane-width);
      // min-width: var(--dock-fixed-pane-width);
      left: calc(var(--left-area-width) + 1px);
      background-color: var(--color-left-float-pane-background, var(--color-pane-background));
      box-shadow: 4px 6px 6px 0 var(--color-block-background-shallow, rgba(31, 50, 88, 0.08));
      z-index: 820;
      display: none;
      // padding-top: 36px;
      &.lc-area-visible {
        display: block;
      }
    }
    .lc-left-area, .lc-workspace-left-area {
      height: 100%;
      width: var(--workspace-left-area-width, --left-area-width);
      display: none;
      flex-shrink: 0;
      flex-direction: column;
      justify-content: space-between;
      overflow: hidden;
      background-color: var(--color-left-area-background, var(--color-pane-background));

      &.lc-workspace-left-area {
        background-color: var(--color-workspace-left-area-background, var(--color-pane-background));
      }
      &.lc-area-visible {
        display: flex;
      }
      .lc-left-area-top,
      .lc-left-area-bottom {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        color: var(--color-text);

        .lc-title {
          flex-direction: column;
          width: calc(var(--left-area-width) - 2px);
          height: 46px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &.has-tip {
            cursor: pointer;
          }
          &.actived {
            color: var(--color-brand, #0079f2);
          }
          &.disabled {
            opacity: 0.4;
          }
          .lc-title-icon {
            height: 20px;
            width: 20px;
            margin: 0;
            .next-icon:before {
              line-height: 1 !important;
            }
          }
        }
      }
      .lc-left-area-top {
        padding-top: 12px;
      }
      .lc-left-area-bottom {
        padding-bottom: 12px;
      }
    }
    .lc-left-fixed-pane {
      width: var(--dock-fixed-pane-width);
      background-color: var(--color-pane-background);
      height: 100%;
      display: none;
      flex-shrink: 0;
      position: relative;
      z-index: 820;
      &.lc-area-visible {
        display: block;
      }
    }
    .lc-left-area.lc-area-visible ~ .lc-left-fixed-pane {
      margin-left: 1px;
    }
    .lc-left-area.lc-area-visible ~ .lc-workbench-center {
      margin-left: 2px;
    }
    .lc-workspace-left-area.lc-area-visible ~ .lc-workspace-workbench-center {
      margin-left: 2px;
    }
    .lc-outline-pane {
      .lc-outline-tree .tree-node .tree-node-title {
        border-bottom: none;
      }
    }
    .lc-workbench-center {
      flex: 1;
      display: flex;
      flex-direction: column;

      .lc-toolbar {
        display: flex;
        height: var(--toolbar-height);
        background-color: var(--color-toolbar-background, var(--color-pane-background));
        padding: var(--toolbar-padding, 8px 16px);
        .lc-toolbar-center {
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
        }
      }
      .lc-main-area {
        flex: 1;
        background-color: var(--color-background);
      }
      .lc-bottom-area {
        height: var(--bottom-area-height);
        background-color: var(--color-pane-background);
        display: none;
        &.lc-area-visible {
          display: block;
        }
      }
    }
    .lc-right-area {
      height: 100%;
      width: var(--right-area-width);
      background-color: var(--color-right-area-background, var(--color-pane-background));
      display: none;
      flex-shrink: 0;
      margin-left: 2px;
      position: relative;
      > .lc-panel {
        position: absolute;
        background-color: var(--color-right-area-background, var(--color-pane-background, #fff));
        left: 0;
        top: 0;
        z-index: 1;
      }
      &.lc-area-visible {
        display: block;
      }
      .lc-settings-tabs {
        > .next-tabs-nav-extra {
          top: 36px !important;
        }
        .lc-settings-tab-item {
          .next-tabs-tab-inner {
            font-size: 12px;
            line-height: 12px;
          }
        }
        .lc-title {
          color: inherit;
          line-height: inherit !important;
        }
      }
      .lc-settings-tabs-content {
        top: 66px;
      }
    }
  }
  .engine-actionitem {
    max-width: 100%;
    color: var(--color-text);
  }
}

.lc-workspace-workbench {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  .lc-workspace-workbench-body {
    flex: 1;
    display: flex;
    min-height: 0;
    position: relative;

    > .lc-left-float-pane {
      left: calc(var(--workspace-left-area-width, var(--left-area-width)) + 1px);
    }

    .lc-workspace-workbench-center {
      flex: 1;
      display: flex;
      flex-direction: column;
      z-index: 10;
      position: relative;
      .lc-toolbar {
        display: flex;
        height: var(--toolbar-height);
        background-color: var(--color-toolbar-background, var(--color-pane-background));
        padding: var(--toolbar-padding, 8px 16px);
        .lc-toolbar-center {
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
        }
      }
      .lc-main-area {
        flex: 1;
      }
      .lc-bottom-area {
        height: var(--bottom-area-height);
        background-color: var(--color-pane-background);
        display: none;
        &.lc-area-visible {
          display: block;
        }
      }
    }

    .lc-workspace-workbench-center-content {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      flex-direction: column;
      display: flex;
      align-content: stretch;
    }

    .lc-workspace-workbench-window {
      position: relative;
      height: 100%;
    }
  }
}
