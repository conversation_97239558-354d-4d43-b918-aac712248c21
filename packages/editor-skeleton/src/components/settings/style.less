.lc-settings-main {
  position: relative;
  height: 100%;
  overflow: hidden;

  ul {
    margin: 0;
  }

  .lc-settings-content {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    overflow-y: auto;
  }

  .lc-setting-stage-back + .lc-settings-content {
    top: 38px;
  }

  .lc-setting-stage-back {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    background: var(--color-block-background-shallow, rgba(31,56,88,.06));
    color: var(--color-title);
    padding: 0 16px;
    user-select: none;
    position: relative;
    margin-bottom: 4px;
    position: absolute;
  }

  .lc-settings-notice {
    text-align: center;
    font-size: 12px;
    font-family: PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica, Arial, sans-serif;
    color: var(--color-text ,rgba(0,0,0,.6));
    padding: 50px 15px 0;
  }

  .lc-settings-navigator {
    height: 30px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--color-line-normal);
    .lc-settings-navigator-icon {
      width: 16px;
      height: 16px;
      * {
        fill: var(--color-icon-normal, rgba(31, 56, 88, 0.4));
      }
    }
    .lc-settings-node-breadcrumb {
      margin-left: 5px;
      .next-breadcrumb {
        display: inline-flex;
        align-items: stretch;
        height: 24px;
      }
      .next-breadcrumb-item {
        display: inline-flex;
        align-items: center;
        cursor: default;
        &:not(:last-child):hover {
          cursor: pointer;
        }
        .next-breadcrumb-text {
          font-size: 12px;
        }
      }
    }
  }

  .lc-settings-body {
    position: absolute;
    top: 30px;
    right: 0;
    left: 0;
    bottom: 0;
    overflow-y: auto;
  }

  // ====== reset fusion-tabs =====
  .lc-settings-tabs {
    position: relative;
    overflow: visible;
    > .next-tabs-nav-extra {
      position: absolute !important;
      top: 40px !important;
      left: 0 !important;
      height: 30px;
      right: 0;
      transform: none !important;

    }
    .next-tabs-nav-container {
      .next-tabs-nav {
        display: flex;
        .next-tabs-tab.lc-settings-tab-item {
          flex: 1;
          min-width: 0;
          outline: none;
          .next-tabs-tab-inner {
            text-align: center;
            padding: 12px 0;
          }
        }
      }
    }
  }

  .lc-settings-tabs-content {
    position: absolute;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    .next-tabs-tabpane {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      overflow-y: auto;
      outline: none !important;
      box-shadow: none !important;
    }
  }
  .lc-outline-pane {
    position: absolute;
    z-index: 100;
    top: 0;
    bottom: 0;
    display: none;
  }
}

.lc-workbench .lc-workbench-body .lc-right-area .lc-settings-hide-tabs {
  .lc-settings-tabs {
    display: none;
  }

  .lc-settings-tabs-content {
    top: 30px;
  }
}

.lc-settings-pane {
  padding-bottom: 50px;
  .next-btn {
    line-height: 1 !important;
  }
}

html.lc-cursor-dragging:not(.lowcode-has-fixed-tree) {
  .lc-settings-main .lc-outline-pane {
    display: block;
  }
}
