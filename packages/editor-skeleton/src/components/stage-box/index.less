@import '../../less-variables.less';

.skeleton-stagebox {
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  .skeleton-stagebox-stage {
    height: auto;
    overflow: hidden;

    transition: transform 0.2s;

    &.skeleton-stagebox-refer {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: auto;
    }

    &.skeleton-stagebox-stageout-left, &.skeleton-stagebox-stagein-right {
      transform: translateX(-100%);
    }

    &.skeleton-stagebox-stageout-right, &.skeleton-stagebox-stagein-left {
      transform: translateX(100%);
    }

    .skeleton-stagebox-stagebacker {
      cursor: pointer;
      height: 30px;
      display: flex;
      align-items: center;
      background: var(--color-block-background-light, @normal-alpha-9);
      justify-content: center;
      position: relative;

      .skeleton-stagebox-stage-arrow {
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%) rotate(90deg);
        opacity: 0.6;
        width: 12px;
      }
      .skeleton-stagebox-stage-title {
        font-weight: bold;
      }
      &:hover {
        background: var(--color-block-background-dark, @normal-alpha-7);
        .skeleton-stagebox-stage-arrow {
          opacity: 1;
        }
      }
      .skeleton-stagebox-stage-exit {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0.6;
      }
    }

    .skeleton-stagebox-stage-content {
      overflow: hidden;
      box-sizing: border-box;
    }

    &.skeleton-stagebox-has-backer {
      .skeleton-stagebox-stage-content {
        padding-top: 30px;
      }
    }
  }
}
