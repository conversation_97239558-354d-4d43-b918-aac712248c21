{"name": "@alilc/lowcode-react-renderer", "version": "1.3.2", "description": "react renderer for ali lowcode engine", "main": "lib/index.js", "module": "es/index.js", "files": ["lib", "es", "dist"], "scripts": {"test": "build-scripts test --config build.test.json", "start": "build-scripts start", "build": "build-scripts build", "build:umd": "NODE_OPTIONS=--max_old_space_size=8192 build-scripts build --config build.umd.json"}, "keywords": ["lowcode", "engine", "react"], "dependencies": {"@alifd/next": "^1.21.16", "@alilc/lowcode-renderer-core": "1.3.2"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@alifd/next": "^1.19.17", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "react": "^16.4.1", "react-dom": "^16.4.1", "react-test-renderer": "^16"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "http", "url": "https://github.com/alibaba/lowcode-engine/tree/main/packages/react-renderer"}, "homepage": "https://github.com/alibaba/lowcode-engine/#readme", "gitHead": "2669f179e6f899d395ce1942d0fe04f9c5ed48a6", "bugs": "https://github.com/alibaba/lowcode-engine/issues"}