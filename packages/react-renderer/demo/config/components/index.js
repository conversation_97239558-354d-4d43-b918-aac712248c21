import Div from './Div';
import Text from './Text';
import A from './A';
import Image from './Image';

import {
  Balloon,
  Button,
  Checkbox,
  Dropdown,
  Grid,
  Menu,
  Select,
  Tab,
  Table,
  Radio,
  Pagination,
  Input,
  Icon,
  Switch,
  Tree,
  NumberPicker,
  Collapse,
  Range,
  Dialog,
  Overlay,
  Search,
  Loading,
  MenuButton,
  Badge,
  Message,
  Slider,
  SplitButton,
  Paragraph,
  Nav,
  Breadcrumb,
  Step,
  DatePicker,
  TimePicker,
  Rating,
  Upload,
  Tag,
  Card,
  Calendar,
  Progress,
  Cascader,
  ConfigProvider,
  Animate,
  CascaderSelect,
  Transfer,
  TreeSelect,
  Timeline,
  VirtualList,
} from '@alifd/next';

const { Row, Col } = Grid;
const {
  Item: MenuItem,
  Group: MenuGroup,
  SubMenu,
  PopupItem: MenuPopupItem,
  CheckboxItem: MenuCheckboxItem,
  RadioItem: MenuRadioItem,
  Divider: MenuDivider,
} = Menu;
const { Item: TabItem } = Tab;
const { Column: TableColumn, ColumnGroup: TableColumnGroup } = Table;
const { Group: ButtonGroup } = Button;
const { Group: RadioGroup } = Radio;
const { Node: TreeNode } = Tree;
const { Panel: CollapsePanel } = Collapse;
const { Tooltip } = Balloon;
const { AutoComplete: SelectAutoComplete, OptionGroup: SelectOptionGroup, Option: SelectOption } = Select;
const { Item: MenuButtonItem } = MenuButton;
const { Item: StepItem } = Step;
const { Item: NavItem, SubNav, PopupItem: NavPopItem, Group: NavGroup } = Nav;
const { Item: BreadcrumbItem } = Breadcrumb;
const { MonthPicker, RangePicker, YearPicker } = DatePicker;
const { Card: UploadCard, Dragger: UploadDragger, Selecter: UploadSelecter } = Upload;
const { Closeable: TagCloseable, Selectable: TagSelectable } = Tag;
const { Popup } = Overlay;
const { Node: TreeSelectNode } = TreeSelect;
const { Item: TimelineItem } = Timeline;

export default {
  Div,
  A,
  Text,
  Image,

  Balloon,
  Tooltip,
  Button,
  ButtonGroup,
  Checkbox,
  Row,
  Col,
  Select,
  SelectAutoComplete,
  SelectOptionGroup,
  SelectOption,
  Dropdown,
  Menu,
  MenuItem,
  MenuGroup,
  MenuDivider,
  SubMenu,
  MenuPopupItem,
  MenuCheckboxItem,
  MenuRadioItem,
  MenuButton,
  MenuButtonItem,
  Loading,
  Tab,
  TabItem,
  Table,
  TableColumn,
  TableColumnGroup,
  Radio,
  RadioGroup,
  Pagination,
  Input,
  Icon,
  Switch,
  Tree,
  TreeNode,
  NumberPicker,
  Collapse,
  Dialog,
  Overlay,
  Popup,
  CollapsePanel,
  Range,
  Search,
  Badge,
  Message,
  Slider,
  SplitButton,
  Paragraph,
  Nav,
  NavItem,
  NavPopItem,
  NavGroup,
  SubNav,
  Breadcrumb,
  BreadcrumbItem,
  Rating,
  Step,
  StepItem,
  DatePicker,
  MonthPicker,
  RangePicker,
  YearPicker,
  TimePicker,
  Upload,
  UploadCard,
  UploadDragger,
  UploadSelecter,
  Tag,
  TagCloseable,
  TagSelectable,
  Card,
  Calendar,
  Progress,
  Cascader,
  ConfigProvider,
  Animate,
  CascaderSelect,
  Transfer,
  TreeSelect,
  TreeSelectNode,
  Timeline,
  TimelineItem,
  VirtualList,
};
