{"entry": {"react-renderer": "src/index"}, "sourceMap": true, "library": "AliLowCodeReactRenderer", "libraryTarget": "umd", "externals": {"react": "var window.React", "react-dom": "var window.ReactDOM", "prop-types": "var window.PropTypes", "@alifd/next": "var Next", "moment": "var window.moment"}, "polyfill": false, "outputDir": "dist", "vendor": false, "ignoreHtmlTemplate": true, "plugins": ["build-plugin-react-app", ["build-plugin-fusion", {"externalNext": "umd"}], ["build-plugin-moment-locales", {"locales": ["zh-cn"]}]]}