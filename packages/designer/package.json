{"name": "@alilc/lowcode-designer", "version": "1.3.2", "description": "Designer for Ali LowCode Engine", "main": "lib/index.js", "module": "es/index.js", "files": ["lib", "es"], "scripts": {"build": "build-scripts build", "test": "build-scripts test --config build.test.json", "test:cov": "build-scripts test --config build.test.json --jest-coverage"}, "license": "MIT", "dependencies": {"@alilc/lowcode-editor-core": "1.3.2", "@alilc/lowcode-types": "1.3.2", "@alilc/lowcode-utils": "1.3.2", "classnames": "^2.2.6", "react": "^16", "react-dom": "^16.7.0", "ric-shim": "^1.0.1", "semver": "^7.3.5"}, "devDependencies": {"@alib/build-scripts": "^0.1.29", "@testing-library/react": "^11.2.2", "@types/classnames": "^2.2.7", "@types/enzyme": "^3.10.12", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/jest": "^26.0.16", "@types/lodash": "^4.14.165", "@types/medium-editor": "^5.0.3", "@types/node": "^13.7.1", "@types/react": "^16", "@types/react-dom": "^16", "@types/semver": "7.3.9", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.5", "jest": "^26.6.3", "lodash": "^4.17.20", "moment": "^2.29.1", "typescript": "^4.0.3"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "http", "url": "https://github.com/alibaba/lowcode-engine/tree/main/packages/designer"}, "gitHead": "2669f179e6f899d395ce1942d0fe04f9c5ed48a6", "bugs": "https://github.com/alibaba/lowcode-engine/issues", "homepage": "https://github.com/alibaba/lowcode-engine/#readme"}