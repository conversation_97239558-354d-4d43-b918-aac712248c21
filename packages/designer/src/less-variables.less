/*
 * 基础的 DPL 定义使用了 kuma base 的定义，参考：
 * https://github.com/uxcore/kuma-base/tree/master/variables
 */

/**
 * ===========================================================
 * ==================== Font Family ==========================
 * ===========================================================
 */

/*
 * @font-family: "STHeiti", "Microsoft Yahei", "Lucida Grande", "Lucida Sans Unicode", Helvetica, Arial, Verdana, sans-serif;
 */

@font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Helvetica, Arial, sans-serif;
@font-family-code: Monaco, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Helvetica, Arial,
  sans-serif;

/**
 * ===========================================================
 * ===================== Color DPL ===========================
 * ===========================================================
  */

@brand-color-1: rgba(0, 108, 255, 1);
@brand-color-2: rgba(25, 122, 255, 1);
@brand-color-3: rgba(0, 96, 229, 1);

@brand-color-1-3: rgba(0, 108, 255, 0.6);
@brand-color-1-4: rgba(0, 108, 255, 0.4);
@brand-color-1-5: rgba(0, 108, 255, 0.3);
@brand-color-1-6: rgba(0, 108, 255, 0.2);
@brand-color-1-7: rgba(0, 108, 255, 0.1);

@brand-color: @brand-color-1;

@white-alpha-1: rgb(255, 255, 255); // W-1
@white-alpha-2: rgba(255, 255, 255, 0.8); // W-2 A80
@white-alpha-3: rgba(255, 255, 255, 0.6); // W-3 A60
@white-alpha-4: rgba(255, 255, 255, 0.4); // W-4 A40
@white-alpha-5: rgba(255, 255, 255, 0.3); // W-5 A30
@white-alpha-6: rgba(255, 255, 255, 0.2); // W-6 A20
@white-alpha-7: rgba(255, 255, 255, 0.1); // W-7 A10
@white-alpha-8: rgba(255, 255, 255, 0.06); // W-8 A6

@dark-alpha-1: rgba(0, 0, 0, 1); // D-1 A100
@dark-alpha-2: rgba(0, 0, 0, 0.8); // D-2 A80
@dark-alpha-3: rgba(0, 0, 0, 0.6); // D-3 A60
@dark-alpha-4: rgba(0, 0, 0, 0.4); // D-4 A40
@dark-alpha-5: rgba(0, 0, 0, 0.3); // D-5 A30
@dark-alpha-6: rgba(0, 0, 0, 0.2); // D-6 A20
@dark-alpha-7: rgba(0, 0, 0, 0.1); // D-7 A10
@dark-alpha-8: rgba(0, 0, 0, 0.06); // D-8 A6
@dark-alpha-9: rgba(0, 0, 0, 0.04); // D-9 A4

@normal-alpha-1: rgba(31, 56, 88, 1); // N-1 A100
@normal-alpha-2: rgba(31, 56, 88, 0.8); // N-2 A80
@normal-alpha-3: rgba(31, 56, 88, 0.6); // N-3 A60
@normal-alpha-4: rgba(31, 56, 88, 0.4); // N-4 A40
@normal-alpha-5: rgba(31, 56, 88, 0.3); // N-5 A30
@normal-alpha-6: rgba(31, 56, 88, 0.2); // N-6 A20
@normal-alpha-7: rgba(31, 56, 88, 0.1); // N-7 A10
@normal-alpha-8: rgba(31, 56, 88, 0.06); // N-8 A6
@normal-alpha-9: rgba(31, 56, 88, 0.04); // N-9 A4

@normal-3: #77879c;
@normal-4: #a3aebd;
@normal-5: #bac3cc;
@normal-6: #d1d7de;

@gray-dark: #333; // N2_4
@gray: #666; // N2_3
@gray-light: #999; // N2_2
@gray-lighter: #ccc; // N2_1

@brand-secondary: #2c2f33; // B2_3
// 补色
@brand-complement: #00b3e8; // B3_1
// 复合
@brand-comosite: #00c587; // B3_2
// 浓度
@brand-deep: #73461d; // B3_3

// F1-1
@brand-danger: rgb(240, 70, 49);
// F1-2 (10% white)
@brand-danger-hover: rgba(240, 70, 49, 0.9);
// F1-3 (5% black)
@brand-danger-focus: rgba(240, 70, 49, 0.95);

// F2-1
@brand-warning: rgb(250, 189, 14);
// F3-1
@brand-success: rgb(102, 188, 92);
// F4-1
@brand-link: rgb(102, 188, 92);
// F4-2
@brand-link-hover: #2e76a6;

// F1-1-7 A10
@brand-danger-alpha-7: rgba(240, 70, 49, 0.1);
// F1-1-8 A6
@brand-danger-alpha-8: rgba(240, 70, 49, 0.8);
// F2-1-2 A80
@brand-warning-alpha-2: rgba(250, 189, 14, 0.8);
// F2-1-7 A10
@brand-warning-alpha-7: rgba(250, 189, 14, 0.1);
// F3-1-2 A80
@brand-success-alpha-2: rgba(102, 188, 92, 0.8);
// F3-1-7 A10
@brand-success-alpha-7: rgba(102, 188, 92, 0.1);
// F4-1-7 A10
@brand-link-alpha-7: rgba(102, 188, 92, 0.1);

// 文本色
@text-primary-color: @dark-alpha-3;
@text-secondary-color: @normal-alpha-3;
@text-thirdary-color: @dark-alpha-4;
@text-disabled-color: @normal-alpha-5;
@text-helper-color: @dark-alpha-4;
@text-danger-color: @brand-danger;
@text-ali-color: #ec6c00;

/**
  * ===========================================================
  * =================== Shadow Box ============================
  * ===========================================================
  */

@box-shadow-1: 0 1px 4px 0 rgba(31, 56, 88, 0.15); // 1 级阴影，物体由原来存在于底面的物体展开，物体和底面关联紧密
@box-shadow-2: 0 2px 10px 0 rgba(31, 56, 88, 0.15); // 2 级阴影，hover状态，物体层级较高
@box-shadow-3: 0 4px 15px 0 rgba(31, 56, 88, 0.15); // 3 级阴影，当物体层级高于所有界面元素，弹窗用

/**
  * ===========================================================
  * ================= FontSize of Level =======================
  * ===========================================================
  */

@fontSize-1: 26px;
@fontSize-2: 20px;
@fontSize-3: 16px;
@fontSize-4: 14px;
@fontSize-5: 12px;

@fontLineHeight-1: 38px;
@fontLineHeight-2: 30px;
@fontLineHeight-3: 26px;
@fontLineHeight-4: 24px;
@fontLineHeight-5: 20px;

/**
  * ===========================================================
  * ================= FontSize of Level =======================
  * ===========================================================
  */

@global-border-radius: 3px;
@input-border-radius: 3px;
@popup-border-radius: 6px;

/**
  * ===========================================================
  * ===================== Transistion =========================
  * ===========================================================
  */

@transition-duration: 0.3s;
@transition-ease: cubic-bezier(0.23, 1, 0.32, 1);
@transition-delay: 0s;

/**
  * ===========================================================
  * ================ Global Configruations ====================
  * ===========================================================
  */

@topPaneHeight: 48px;
@actionpane-height: 48px;
@tabPaneWidth: 260px;
@input-standard-height: 32px;
@dockpane-width: 48px;

/**
  * ===========================================================
  * =================== Deprecated Items ======================
  * ===========================================================
  */

@head-bgcolor: @white-alpha-1;
@pane-bgcolor: @white-alpha-1;
@pane-dark-bgcolor: @white-alpha-1;
@pane-bdcolor: @normal-4;
@blank-bgcolor: @normal-5;
@title-bgcolor: @white-alpha-1;
@title-bdcolor: transparent;
@section-bgcolor: transparent;
@section-bdcolor: @white-alpha-1;
@button-bgcolor: @white-alpha-1;
@button-bdcolor: transparent;
@button-blue-color: @brand-color;
@button-blue-hover-color: @brand-color;
@sub-title-bgcolor: @white-alpha-1;
@sub-title-bdcolor: transparent;
@text-color: @text-primary-color;
@icon-color: @gray;
@icon-color-active: @gray-light;
@ghost-bgcolor: @dark-alpha-3;
@input-bgcolor: transparent;
@input-bdcolor: @normal-alpha-5;
@hover-color: #5a99cc;
@active-color: #5a99cc;
@disabled-color: #666;
@setter-popup-bg: rgb(80, 86, 109);
