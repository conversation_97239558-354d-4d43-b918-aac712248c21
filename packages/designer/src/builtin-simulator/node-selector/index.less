@import '../../less-variables.less';

// 样式直接沿用之前的样式，优化了下命名
.instance-node-selector {
  position: relative;
  margin-right: 2px;
  color: var(--color-icon-white, @title-bgcolor);
  border-radius: @global-border-radius;
  pointer-events: auto;
  flex-grow: 0;
  flex-shrink: 0;

  svg {
    width: 16px;
    height: 16px;
    margin-right: 5px;
    flex-grow: 0;
    flex-shrink: 0;
    max-width: inherit;
    path {
      fill: var(--color-icon-white, @title-bgcolor);
    }
  }
  .instance-node-selector-current {
    background: var(--color-brand, @brand-color-1);
    padding: 0 6px;
    display: flex;
    align-items: center;
    height: 20px;
    cursor: pointer;
    color: var(--color-icon-white, @title-bgcolor);
    border-radius: 3px;

    &-title {
      padding-right: 6px;
      color: var(--color-icon-white, @title-bgcolor);
    }
  }
  .instance-node-selector-list {
    position: absolute;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;
  }
  .instance-node-selector-node {
    height: 20px;
    margin-top: 2px;
    &-content {
      padding-left: 6px;
      background: var(--color-layer-tooltip-background, #78869a);
      display: inline-flex;
      border-radius: 3px;
      align-items: center;
      height: 20px;
      color: var(--color-icon-white, @title-bgcolor);
      cursor: pointer;
      overflow: visible;
    }
    &-title {
      padding-right: 6px;
      // margin-left: 5px;
      color: var(--color-icon-white, @title-bgcolor);
      cursor: pointer;
      overflow: visible;
    }
    &:hover {
      opacity: 0.8;
    }
  }

  &:hover {
    .instance-node-selector-current {
      color: ar(--color-text-reverse, @white-alpha-2);
    }
    .instance-node-selector-popup {
      visibility: visible;
      opacity: 1;
      transition: 0.2s all ease-in;
    }
  }
}
