export default {
  componentName: 'Page',
  id: 'page',
  title: 'hey, i\' a page!',
  props: {
    extensions: {
      启用页头: true,
    },
    pageStyle: {
      backgroundColor: '#f2f3f5',
    },
    containerStyle: {},
    className: 'page_kgaqfbm4',
    templateVersion: '1.0.0',
  },
  lifeCycles: {
    constructor: {
      type: 'js',
      compiled:
        "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
      source:
        "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
    },
  },
  condition: true,
  css:
    'body{background-color:#f2f3f5}.card_kgaqfbm5 {\n  margin-bottom: 12px;\n}.card_kgaqfbm6 {\n  margin-bottom: 12px;\n}.button_kgaqfbm7 {\n  margin-right: 16px;\n  width: 80px\n}.button_kgaqfbm8 {\n  width: 80px;\n}.div_kgaqfbm9 {\n  display: flex;\n  align-items: flex-start;\n  justify-content: center;\n  background: #fff;\n  padding: 20px 0;\n}',
  methods: {
    __initMethods__: {
      type: 'js',
      source: 'function (exports, module) { /*set actions code here*/ }',
      compiled: 'function (exports, module) { /*set actions code here*/ }',
    },
  },
  dataSource: {
    offline: [],
    globalConfig: {
      fit: {
        compiled: '',
        source: '',
        type: 'js',
        error: {},
      },
    },
    online: [],
    sync: true,
    list: [],
  },
  children: [
    {
      componentName: 'RootHeader',
      id: 'node_k1ow3cba',
      props: {},
      condition: true,
      children: [
        {
          componentName: 'PageHeader',
          id: 'node_k1ow3cbd',
          props: {
            extraContent: '',
            __slot__extraContent: false,
            __slot__action: false,
            title: {
              // type: 'JSSlot',
              value: [
                {
                  componentName: 'Text',
                  id: 'node_k1ow3cbf',
                  props: {
                    showTitle: false,
                    behavior: 'NORMAL',
                    content: {
                      use: 'zh-CN',
                      'en-US': 'Title',
                      'zh-CN': '个人信息',
                      type: 'i18n',
                    },
                    __style__: {},
                    fieldId: 'text_k1ow3h1j',
                    maxLine: 0,
                  },
                  condition: true,
                },
              ],
            },
            content: '',
            __slot__logo: false,
            __slot__crumb: false,
            crumb: '',
            tab: '',
            logo: '',
            action: '',
            __slot__tab: false,
            __style__: {},
            __slot__content: false,
            fieldId: 'pageHeader_k1ow3h1i',
            subTitle: false,
          },
          condition: true,
        },
      ],
    },
    {
      componentName: 'RootContent',
      id: 'node_k1ow3cbb',
      props: {
        contentBgColor: 'transparent',
        contentPadding: '0',
        contentMargin: '20',
      },
      condition: true,
      children: [
        {
          componentName: 'Form',
          id: 'form',
          extraPropA: 'extraPropA',
          props: {
            size: 'medium',
            labelAlign: 'top',
            autoValidate: true,
            scrollToFirstError: true,
            autoUnmount: true,
            behavior: 'NORMAL',
            dataSource: {
              type: 'variable',
              variable: 'state.formData',
            },
            obj: {
              a: 1,
              b: false,
              c: 'string',
            },
            __style__: {},
            fieldId: 'form',
            fieldOptions: {},
            slotA: '',
          },
          condition: true,
          children: [
            {
              componentName: 'Card',
              id: 'node_k1ow3cbj',
              props: {
                __slot__title: false,
                subTitle: {
                  use: 'zh-CN',
                  'en-US': '',
                  'zh-CN': '',
                  type: 'i18n',
                },
                __slot__subTitle: false,
                extra: {
                  use: 'zh-CN',
                  'zh-CN': '',
                  type: 'i18n',
                },
                className: 'card_kgaqfbm5',
                title: {
                  use: 'zh-CN',
                  'en-US': 'Title',
                  'zh-CN': '基本信息',
                  type: 'i18n',
                },
                __slot__extra: false,
                showHeadDivider: true,
                __style__: ':root {\n  margin-bottom: 12px;\n}',
                showTitleBullet: true,
                contentHeight: '',
                fieldId: 'card_k1ow3h1l',
                dividerNoInset: false,
              },
              condition: true,
              children: [
                {
                  componentName: 'CardContent',
                  id: 'node_k1ow3cbk',
                  props: {},
                  condition: true,
                  children: [
                    {
                      componentName: 'ColumnsLayout',
                      id: 'node_k1ow3cbw',
                      props: {
                        layout: '6:6',
                        columnGap: '20',
                        rowGap: 0,
                        __style__: {},
                        fieldId: 'columns_k1ow3h1v',
                      },
                      condition: true,
                      children: [
                        {
                          componentName: 'Column',
                          id: 'node_k1ow3cbx',
                          props: {
                            colSpan: '',
                            __style__: {},
                            fieldId: 'column_k1p1bnjm',
                          },
                          condition: true,
                          children: [
                            {
                              componentName: 'TextField',
                              id: 'node_k1ow3cbz',
                              props: {
                                fieldName: 'name',
                                hasClear: false,
                                autoFocus: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                trim: false,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please input',
                                  'zh-CN': '请输入',
                                  type: 'i18n',
                                },
                                state: '',
                                behavior: 'NORMAL',
                                value: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                addonBefore: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                validation: [
                                  {
                                    type: 'required',
                                  },
                                ],
                                hasLimitHint: false,
                                cutString: false,
                                __style__: {},
                                fieldId: 'textField_k1ow3h1w',
                                htmlType: 'input',
                                autoHeight: false,
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'TextField',
                                  'zh-CN': '姓名',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                rows: 4,
                                addonAfter: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                wrapperColOffset: 0,
                                size: 'medium',
                                labelAlign: 'top',
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                              },
                              condition: true,
                            },
                            {
                              componentName: 'TextField',
                              id: 'node_k1ow3cc1',
                              props: {
                                fieldName: 'englishName',
                                hasClear: false,
                                autoFocus: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                trim: false,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please input',
                                  'zh-CN': '请输入',
                                  type: 'i18n',
                                },
                                state: '',
                                behavior: 'NORMAL',
                                value: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                addonBefore: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                validation: [],
                                hasLimitHint: false,
                                cutString: false,
                                __style__: {},
                                fieldId: 'textField_k1ow3h1y',
                                htmlType: 'input',
                                autoHeight: false,
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'TextField',
                                  'zh-CN': '英文名',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                rows: 4,
                                addonAfter: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                wrapperColOffset: 0,
                                size: 'medium',
                                labelAlign: 'top',
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                              },
                              condition: true,
                            },
                            {
                              componentName: 'TextField',
                              id: 'node_k1ow3cc3',
                              props: {
                                fieldName: 'jobTitle',
                                hasClear: false,
                                autoFocus: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                trim: false,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please input',
                                  'zh-CN': '请输入',
                                  type: 'i18n',
                                },
                                state: '',
                                behavior: 'NORMAL',
                                value: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                addonBefore: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                validation: [],
                                hasLimitHint: false,
                                cutString: false,
                                __style__: {},
                                fieldId: 'textField_k1ow3h20',
                                htmlType: 'input',
                                autoHeight: false,
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'TextField',
                                  'zh-CN': '职位',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                rows: 4,
                                addonAfter: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                wrapperColOffset: 0,
                                size: 'medium',
                                labelAlign: 'top',
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                              },
                              condition: true,
                            },
                          ],
                        },
                        {
                          componentName: 'Column',
                          id: 'node_k1ow3cby',
                          props: {
                            colSpan: '',
                            __style__: {},
                            fieldId: 'column_k1p1bnjn',
                          },
                          condition: true,
                          children: [
                            {
                              componentName: 'TextField',
                              id: 'node_k1ow3cc2',
                              props: {
                                fieldName: 'nickName',
                                hasClear: false,
                                autoFocus: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                trim: false,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please input',
                                  'zh-CN': '请输入',
                                  type: 'i18n',
                                },
                                state: '',
                                behavior: 'NORMAL',
                                value: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                addonBefore: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                validation: [],
                                hasLimitHint: false,
                                cutString: false,
                                __style__: {},
                                fieldId: 'textField_k1ow3h1z',
                                htmlType: 'input',
                                autoHeight: false,
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'TextField',
                                  'zh-CN': '花名',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                rows: 4,
                                addonAfter: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                wrapperColOffset: 0,
                                size: 'medium',
                                labelAlign: 'top',
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                              },
                              condition: true,
                            },
                            {
                              componentName: 'SelectField',
                              id: 'node_k1ow3cc0',
                              props: {
                                fieldName: 'gender',
                                hasClear: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                mode: 'single',
                                showSearch: false,
                                autoWidth: true,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please select',
                                  'zh-CN': '请选择',
                                  type: 'i18n',
                                },
                                hasBorder: true,
                                behavior: 'NORMAL',
                                value: '',
                                validation: [
                                  {
                                    type: 'required',
                                  },
                                ],
                                __style__: {},
                                fieldId: 'select_k1ow3h1x',
                                notFoundContent: {
                                  use: 'zh-CN',
                                  type: 'i18n',
                                },
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'SelectField',
                                  'zh-CN': '性别',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                wrapperColOffset: 0,
                                hasSelectAll: false,
                                hasArrow: true,
                                size: 'medium',
                                labelAlign: 'top',
                                filterLocal: true,
                                dataSource: [
                                  {
                                    defaultChecked: false,
                                    text: {
                                      'en-US': 'Option 1',
                                      'zh-CN': '男',
                                      type: 'i18n',
                                      __sid__: 'param_k1owc4tb',
                                    },
                                    __sid__: 'serial_k1owc4t1',
                                    value: 'M',
                                    sid: 'opt_k1owc4t2',
                                  },
                                  {
                                    defaultChecked: false,
                                    text: {
                                      'en-US': 'Option 2',
                                      'zh-CN': '女',
                                      type: 'i18n',
                                      __sid__: 'param_k1owc4tf',
                                    },
                                    __sid__: 'serial_k1owc4t2',
                                    value: 'F',
                                    sid: 'opt_k1owc4t3',
                                  },
                                ],
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                                searchDelay: 300,
                              },
                              condition: true,
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              componentName: 'Card',
              id: 'node_k1ow3cbl',
              props: {
                __slot__title: false,
                subTitle: {
                  use: 'zh-CN',
                  'en-US': '',
                  'zh-CN': '',
                  type: 'i18n',
                },
                __slot__subTitle: false,
                extra: {
                  use: 'zh-CN',
                  'zh-CN': '',
                  type: 'i18n',
                },
                className: 'card_kgaqfbm6',
                title: {
                  use: 'zh-CN',
                  'en-US': 'Title',
                  'zh-CN': '部门信息',
                  type: 'i18n',
                },
                __slot__extra: false,
                showHeadDivider: true,
                __style__: ':root {\n  margin-bottom: 12px;\n}',
                showTitleBullet: true,
                contentHeight: '',
                fieldId: 'card_k1ow3h1m',
                dividerNoInset: false,
              },
              condition: true,
              children: [
                {
                  componentName: 'CardContent',
                  id: 'node_k1ow3cbm',
                  props: {},
                  condition: true,
                  children: [
                    {
                      componentName: 'TextField',
                      id: 'node_k1ow3cc4',
                      props: {
                        fieldName: 'department',
                        hasClear: false,
                        autoFocus: false,
                        tips: {
                          'en-US': '',
                          'zh-CN': '',
                          type: 'i18n',
                        },
                        trim: false,
                        labelTextAlign: 'right',
                        placeholder: {
                          use: 'zh-CN',
                          'en-US': 'please input',
                          'zh-CN': '请输入',
                          type: 'i18n',
                        },
                        state: '',
                        behavior: 'NORMAL',
                        value: {
                          use: 'zh-CN',
                          'zh-CN': '',
                          type: 'i18n',
                        },
                        addonBefore: {
                          use: 'zh-CN',
                          'zh-CN': '',
                          type: 'i18n',
                        },
                        validation: [],
                        hasLimitHint: false,
                        cutString: false,
                        __style__: {},
                        fieldId: 'textField_k1ow3h21',
                        htmlType: 'input',
                        autoHeight: false,
                        labelColOffset: 0,
                        label: {
                          use: 'zh-CN',
                          'en-US': 'TextField',
                          'zh-CN': '所属部门',
                          type: 'i18n',
                        },
                        __category__: 'form',
                        labelColSpan: 4,
                        wrapperColSpan: 0,
                        rows: 4,
                        addonAfter: {
                          use: 'zh-CN',
                          'zh-CN': '',
                          type: 'i18n',
                        },
                        wrapperColOffset: 0,
                        size: 'medium',
                        labelAlign: 'top',
                        __useMediator: 'value',
                        labelTipsTypes: 'none',
                        labelTipsIcon: '',
                        labelTipsText: {
                          type: 'i18n',
                          use: 'zh-CN',
                          'en-US': '',
                          'zh-CN': '',
                        },
                      },
                      condition: true,
                    },
                    {
                      componentName: 'ColumnsLayout',
                      id: 'node_k1ow3cc5',
                      props: {
                        layout: '6:6',
                        columnGap: '20',
                        rowGap: 0,
                        __style__: {},
                        fieldId: 'columns_k1ow3h22',
                      },
                      condition: true,
                      children: [
                        {
                          componentName: 'Column',
                          id: 'node_k1ow3cc6',
                          props: {
                            colSpan: '',
                            __style__: {},
                            fieldId: 'column_k1p1bnjo',
                          },
                          condition: true,
                          children: [
                            {
                              componentName: 'TextField',
                              id: 'node_k1ow3cc8',
                              props: {
                                fieldName: 'leader',
                                hasClear: false,
                                autoFocus: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                trim: false,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please input',
                                  'zh-CN': '请输入',
                                  type: 'i18n',
                                },
                                state: '',
                                behavior: 'NORMAL',
                                value: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                addonBefore: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                validation: [],
                                hasLimitHint: false,
                                cutString: false,
                                __style__: {},
                                fieldId: 'textField_k1ow3h23',
                                htmlType: 'input',
                                autoHeight: false,
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'TextField',
                                  'zh-CN': '主管',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                rows: 4,
                                addonAfter: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                wrapperColOffset: 0,
                                size: 'medium',
                                labelAlign: 'top',
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                              },
                              condition: true,
                            },
                          ],
                        },
                        {
                          componentName: 'Column',
                          id: 'node_k1ow3cc7',
                          props: {
                            colSpan: '',
                            __style__: {},
                            fieldId: 'column_k1p1bnjp',
                          },
                          condition: true,
                          children: [
                            {
                              componentName: 'TextField',
                              id: 'node_k1ow3cc9',
                              props: {
                                fieldName: 'hrg',
                                hasClear: false,
                                autoFocus: false,
                                tips: {
                                  'en-US': '',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                trim: false,
                                labelTextAlign: 'right',
                                placeholder: {
                                  use: 'zh-CN',
                                  'en-US': 'please input',
                                  'zh-CN': '请输入',
                                  type: 'i18n',
                                },
                                state: '',
                                behavior: 'NORMAL',
                                value: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                addonBefore: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                validation: [],
                                hasLimitHint: false,
                                cutString: false,
                                __style__: {},
                                fieldId: 'textField_k1ow3h24',
                                htmlType: 'input',
                                autoHeight: false,
                                labelColOffset: 0,
                                label: {
                                  use: 'zh-CN',
                                  'en-US': 'TextField',
                                  'zh-CN': 'HRG',
                                  type: 'i18n',
                                },
                                __category__: 'form',
                                labelColSpan: 4,
                                wrapperColSpan: 0,
                                rows: 4,
                                addonAfter: {
                                  use: 'zh-CN',
                                  'zh-CN': '',
                                  type: 'i18n',
                                },
                                wrapperColOffset: 0,
                                size: 'medium',
                                labelAlign: 'top',
                                __useMediator: 'value',
                                labelTipsTypes: 'none',
                                labelTipsIcon: '',
                                labelTipsText: {
                                  type: 'i18n',
                                  use: 'zh-CN',
                                  'en-US': '',
                                  'zh-CN': '',
                                },
                              },
                              condition: true,
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              componentName: 'Div',
              id: 'node_k1ow3cbo',
              props: {
                className: 'div_kgaqfbm9',
                behavior: 'NORMAL',
                __style__:
                  ':root {\n  display: flex;\n  align-items: flex-start;\n  justify-content: center;\n  background: #fff;\n  padding: 20px 0;\n}',
                events: {},
                fieldId: 'div_k1ow3h1o',
                useFieldIdAsDomId: false,
                customClassName: '',
              },
              condition: true,
              children: [
                {
                  componentName: 'Button',
                  id: 'node_k1ow3cbn',
                  props: {
                    triggerEventsWhenLoading: false,
                    onClick: {
                      rawType: 'events',
                      type: 'JSExpression',
                      value: 'this.utils.legaoBuiltin.execEventFlow.bind(this, [this.submit])',
                      events: [
                        {
                          name: 'submit',
                          id: 'submit',
                          params: {},
                          type: 'actionRef',
                          uuid: '1570966253282_0',
                        },
                      ],
                    },
                    size: 'medium',
                    baseIcon: '',
                    otherIcon: '',
                    className: 'button_kgaqfbm7',
                    type: 'primary',
                    behavior: 'NORMAL',
                    loading: false,
                    content: {
                      use: 'zh-CN',
                      'en-US': 'Button',
                      'zh-CN': '提交',
                      type: 'i18n',
                    },
                    __style__: ':root {\n  margin-right: 16px;\n  width: 80px\n}',
                    fieldId: 'button_k1ow3h1n',
                  },
                  condition: true,
                },
                {
                  componentName: 'Button',
                  id: 'node_k1ow3cbp',
                  props: {
                    triggerEventsWhenLoading: false,
                    size: 'medium',
                    baseIcon: '',
                    otherIcon: '',
                    className: 'button_kgaqfbm8',
                    type: 'normal',
                    behavior: 'NORMAL',
                    loading: false,
                    content: {
                      use: 'zh-CN',
                      'en-US': 'Button',
                      'zh-CN': '取消',
                      type: 'i18n',
                    },
                    __style__: ':root {\n  width: 80px;\n}',
                    fieldId: 'button_k1ow3h1p',
                    greeting: {
                      // type: 'JSSlot',
                      value: [{
                        componentName: 'Text',
                        props: {},
                      }],
                    },
                  },
                  condition: true,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      componentName: 'RootFooter',
      id: 'node_k1ow3cbc',
      props: {},
      condition: true,
    },
  ],
  i18n: {
    'zh-CN': {
      'i18n-jwg27yo4': '你好',
      'i18n-jwg27yo3': '中国',
    },
    'en-US': {
      'i18n-jwg27yo4': 'Hello',
      'i18n-jwg27yo3': 'China',
    },
  },
};
