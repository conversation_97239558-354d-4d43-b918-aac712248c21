// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`setting-field 测试 纯粹的 UnitTest 常规方法 1`] = `
Object {
  "extraProps": Object {
    "defaultValue": "NORMAL",
    "display": "inline",
  },
  "name": "behavior",
  "setter": Object {
    "componentName": "MixedSetter",
    "props": Object {
      "setters": Array [
        Object {
          "_owner": null,
          "key": null,
          "props": Object {
            "cancelable": false,
            "loose": false,
            "options": Array [
              Object {
                "title": "普通",
                "value": "NORMAL",
              },
              Object {
                "title": "隐藏",
                "value": "HIDDEN",
              },
            ],
          },
          "ref": null,
        },
        "VariableSetter",
      ],
    },
  },
  "title": "默认状态",
  "type": "field",
}
`;

exports[`setting-field 测试 纯粹的 UnitTest 常规方法 2`] = `
Object {
  "extraProps": Object {
    "defaultValue": "NORMAL",
    "display": "inline",
  },
  "name": "behavior",
  "setter": Object {
    "componentName": "MixedSetter",
    "props": Object {
      "setters": Array [
        Object {
          "_owner": null,
          "key": null,
          "props": Object {
            "cancelable": false,
            "loose": false,
            "options": Array [
              Object {
                "title": "普通",
                "value": "NORMAL",
              },
              Object {
                "title": "隐藏",
                "value": "HIDDEN",
              },
            ],
          },
          "ref": null,
        },
        "VariableSetter",
      ],
    },
  },
  "title": "默认状态",
  "type": "field",
}
`;
