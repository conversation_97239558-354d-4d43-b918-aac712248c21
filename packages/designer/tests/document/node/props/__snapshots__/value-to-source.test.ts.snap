// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`valueToSource 1`] = `"1"`;

exports[`valueToSource 2`] = `"true"`;

exports[`valueToSource 3`] = `"[]"`;

exports[`valueToSource 4`] = `
"[{
  \\"a\\": 1
}]"
`;

exports[`valueToSource 5`] = `
"{
  \\"a\\": 1
}"
`;

exports[`valueToSource 6`] = `"null"`;

exports[`valueToSource 7`] = `"() => {}"`;

exports[`valueToSource 8`] = `"new Map()"`;

exports[`valueToSource 9`] = `"new Set()"`;

exports[`valueToSource 10`] = `"/haha/"`;

exports[`valueToSource 11`] = `"\\"hahah\\""`;

exports[`valueToSource 12`] = `"Symbol(\\"haha\\")"`;

exports[`valueToSource 13`] = `"undefined"`;

exports[`valueToSource 14`] = `"new Date(\\"2020-12-11T10:03:18.520Z\\")"`;
