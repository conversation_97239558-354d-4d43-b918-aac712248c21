// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`document-model 测试 各种方法测试 1`] = `
Object {
  "componentsMap": Array [
    Object {
      "componentName": "PageHeader",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "RootHeader",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "TextField",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "Column",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "SelectField",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "ColumnsLayout",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "CardContent",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "Card",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "Button",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "Div",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "Form",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "RootContent",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "RootFooter",
      "devMode": "lowCode",
    },
    Object {
      "componentName": "Page",
      "devMode": "lowCode",
    },
  ],
  "componentsTree": Array [
    Object {
      "children": Array [
        Object {
          "children": Array [
            Object {
              "componentName": "PageHeader",
              "condition": true,
              "conditionGroup": "",
              "hidden": false,
              "id": "node_k1ow3cbd",
              "isLocked": false,
              "props": Object {
                "__slot__action": false,
                "__slot__content": false,
                "__slot__crumb": false,
                "__slot__extraContent": false,
                "__slot__logo": false,
                "__slot__tab": false,
                "__style__": Object {},
                "action": "",
                "content": "",
                "crumb": "",
                "extraContent": "",
                "fieldId": "pageHeader_k1ow3h1i",
                "logo": "",
                "subTitle": false,
                "tab": "",
                "title": Object {
                  "value": Array [
                    Object {
                      "componentName": "Text",
                      "condition": true,
                      "id": "node_k1ow3cbf",
                      "props": Object {
                        "__style__": Object {},
                        "behavior": "NORMAL",
                        "content": Object {
                          "en-US": "Title",
                          "type": "i18n",
                          "use": "zh-CN",
                          "zh-CN": "个人信息",
                        },
                        "fieldId": "text_k1ow3h1j",
                        "maxLine": 0,
                        "showTitle": false,
                      },
                    },
                  ],
                },
              },
              "title": "",
            },
          ],
          "componentName": "RootHeader",
          "condition": true,
          "conditionGroup": "",
          "hidden": false,
          "id": "node_k1ow3cba",
          "isLocked": false,
          "props": Object {},
          "title": "",
        },
        Object {
          "children": Array [
            Object {
              "children": Array [
                Object {
                  "children": Array [
                    Object {
                      "children": Array [
                        Object {
                          "children": Array [
                            Object {
                              "children": Array [
                                Object {
                                  "componentName": "TextField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cbz",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "addonAfter": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "addonBefore": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "autoFocus": false,
                                    "autoHeight": false,
                                    "behavior": "NORMAL",
                                    "cutString": false,
                                    "fieldId": "textField_k1ow3h1w",
                                    "fieldName": "name",
                                    "hasClear": false,
                                    "hasLimitHint": false,
                                    "htmlType": "input",
                                    "label": Object {
                                      "en-US": "TextField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "姓名",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "placeholder": Object {
                                      "en-US": "please input",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请输入",
                                    },
                                    "rows": 4,
                                    "size": "medium",
                                    "state": "",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "trim": false,
                                    "validation": Array [
                                      Object {
                                        "type": "required",
                                      },
                                    ],
                                    "value": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                                Object {
                                  "componentName": "TextField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cc1",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "addonAfter": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "addonBefore": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "autoFocus": false,
                                    "autoHeight": false,
                                    "behavior": "NORMAL",
                                    "cutString": false,
                                    "fieldId": "textField_k1ow3h1y",
                                    "fieldName": "englishName",
                                    "hasClear": false,
                                    "hasLimitHint": false,
                                    "htmlType": "input",
                                    "label": Object {
                                      "en-US": "TextField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "英文名",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "placeholder": Object {
                                      "en-US": "please input",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请输入",
                                    },
                                    "rows": 4,
                                    "size": "medium",
                                    "state": "",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "trim": false,
                                    "validation": Array [],
                                    "value": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                                Object {
                                  "componentName": "TextField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cc3",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "addonAfter": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "addonBefore": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "autoFocus": false,
                                    "autoHeight": false,
                                    "behavior": "NORMAL",
                                    "cutString": false,
                                    "fieldId": "textField_k1ow3h20",
                                    "fieldName": "jobTitle",
                                    "hasClear": false,
                                    "hasLimitHint": false,
                                    "htmlType": "input",
                                    "label": Object {
                                      "en-US": "TextField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "职位",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "placeholder": Object {
                                      "en-US": "please input",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请输入",
                                    },
                                    "rows": 4,
                                    "size": "medium",
                                    "state": "",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "trim": false,
                                    "validation": Array [],
                                    "value": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                              ],
                              "componentName": "Column",
                              "condition": true,
                              "conditionGroup": "",
                              "hidden": false,
                              "id": "node_k1ow3cbx",
                              "isLocked": false,
                              "props": Object {
                                "__style__": Object {},
                                "colSpan": "",
                                "fieldId": "column_k1p1bnjm",
                              },
                              "title": "",
                            },
                            Object {
                              "children": Array [
                                Object {
                                  "componentName": "TextField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cc2",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "addonAfter": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "addonBefore": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "autoFocus": false,
                                    "autoHeight": false,
                                    "behavior": "NORMAL",
                                    "cutString": false,
                                    "fieldId": "textField_k1ow3h1z",
                                    "fieldName": "nickName",
                                    "hasClear": false,
                                    "hasLimitHint": false,
                                    "htmlType": "input",
                                    "label": Object {
                                      "en-US": "TextField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "花名",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "placeholder": Object {
                                      "en-US": "please input",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请输入",
                                    },
                                    "rows": 4,
                                    "size": "medium",
                                    "state": "",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "trim": false,
                                    "validation": Array [],
                                    "value": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                                Object {
                                  "componentName": "SelectField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cc0",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "autoWidth": true,
                                    "behavior": "NORMAL",
                                    "dataSource": Array [
                                      Object {
                                        "__sid__": "serial_k1owc4t1",
                                        "defaultChecked": false,
                                        "sid": "opt_k1owc4t2",
                                        "text": Object {
                                          "__sid__": "param_k1owc4tb",
                                          "en-US": "Option 1",
                                          "type": "i18n",
                                          "zh-CN": "男",
                                        },
                                        "value": "M",
                                      },
                                      Object {
                                        "__sid__": "serial_k1owc4t2",
                                        "defaultChecked": false,
                                        "sid": "opt_k1owc4t3",
                                        "text": Object {
                                          "__sid__": "param_k1owc4tf",
                                          "en-US": "Option 2",
                                          "type": "i18n",
                                          "zh-CN": "女",
                                        },
                                        "value": "F",
                                      },
                                    ],
                                    "fieldId": "select_k1ow3h1x",
                                    "fieldName": "gender",
                                    "filterLocal": true,
                                    "hasArrow": true,
                                    "hasBorder": true,
                                    "hasClear": false,
                                    "hasSelectAll": false,
                                    "label": Object {
                                      "en-US": "SelectField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "性别",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "mode": "single",
                                    "notFoundContent": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                    },
                                    "placeholder": Object {
                                      "en-US": "please select",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请选择",
                                    },
                                    "searchDelay": 300,
                                    "showSearch": false,
                                    "size": "medium",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "validation": Array [
                                      Object {
                                        "type": "required",
                                      },
                                    ],
                                    "value": "",
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                              ],
                              "componentName": "Column",
                              "condition": true,
                              "conditionGroup": "",
                              "hidden": false,
                              "id": "node_k1ow3cby",
                              "isLocked": false,
                              "props": Object {
                                "__style__": Object {},
                                "colSpan": "",
                                "fieldId": "column_k1p1bnjn",
                              },
                              "title": "",
                            },
                          ],
                          "componentName": "ColumnsLayout",
                          "condition": true,
                          "conditionGroup": "",
                          "hidden": false,
                          "id": "node_k1ow3cbw",
                          "isLocked": false,
                          "props": Object {
                            "__style__": Object {},
                            "columnGap": "20",
                            "fieldId": "columns_k1ow3h1v",
                            "layout": "6:6",
                            "rowGap": 0,
                          },
                          "title": "",
                        },
                      ],
                      "componentName": "CardContent",
                      "condition": true,
                      "conditionGroup": "",
                      "hidden": false,
                      "id": "node_k1ow3cbk",
                      "isLocked": false,
                      "props": Object {},
                      "title": "",
                    },
                  ],
                  "componentName": "Card",
                  "condition": true,
                  "conditionGroup": "",
                  "hidden": false,
                  "id": "node_k1ow3cbj",
                  "isLocked": false,
                  "props": Object {
                    "__slot__extra": false,
                    "__slot__subTitle": false,
                    "__slot__title": false,
                    "__style__": ":root {
  margin-bottom: 12px;
}",
                    "className": "card_kgaqfbm5",
                    "contentHeight": "",
                    "dividerNoInset": false,
                    "extra": Object {
                      "type": "i18n",
                      "use": "zh-CN",
                      "zh-CN": "",
                    },
                    "fieldId": "card_k1ow3h1l",
                    "showHeadDivider": true,
                    "showTitleBullet": true,
                    "subTitle": Object {
                      "en-US": "",
                      "type": "i18n",
                      "use": "zh-CN",
                      "zh-CN": "",
                    },
                    "title": Object {
                      "en-US": "Title",
                      "type": "i18n",
                      "use": "zh-CN",
                      "zh-CN": "基本信息",
                    },
                  },
                  "title": "",
                },
                Object {
                  "children": Array [
                    Object {
                      "children": Array [
                        Object {
                          "componentName": "TextField",
                          "condition": true,
                          "conditionGroup": "",
                          "hidden": false,
                          "id": "node_k1ow3cc4",
                          "isLocked": false,
                          "props": Object {
                            "__category__": "form",
                            "__style__": Object {},
                            "__useMediator": "value",
                            "addonAfter": Object {
                              "type": "i18n",
                              "use": "zh-CN",
                              "zh-CN": "",
                            },
                            "addonBefore": Object {
                              "type": "i18n",
                              "use": "zh-CN",
                              "zh-CN": "",
                            },
                            "autoFocus": false,
                            "autoHeight": false,
                            "behavior": "NORMAL",
                            "cutString": false,
                            "fieldId": "textField_k1ow3h21",
                            "fieldName": "department",
                            "hasClear": false,
                            "hasLimitHint": false,
                            "htmlType": "input",
                            "label": Object {
                              "en-US": "TextField",
                              "type": "i18n",
                              "use": "zh-CN",
                              "zh-CN": "所属部门",
                            },
                            "labelAlign": "top",
                            "labelColOffset": 0,
                            "labelColSpan": 4,
                            "labelTextAlign": "right",
                            "labelTipsIcon": "",
                            "labelTipsText": Object {
                              "en-US": "",
                              "type": "i18n",
                              "use": "zh-CN",
                              "zh-CN": "",
                            },
                            "labelTipsTypes": "none",
                            "placeholder": Object {
                              "en-US": "please input",
                              "type": "i18n",
                              "use": "zh-CN",
                              "zh-CN": "请输入",
                            },
                            "rows": 4,
                            "size": "medium",
                            "state": "",
                            "tips": Object {
                              "en-US": "",
                              "type": "i18n",
                              "zh-CN": "",
                            },
                            "trim": false,
                            "validation": Array [],
                            "value": Object {
                              "type": "i18n",
                              "use": "zh-CN",
                              "zh-CN": "",
                            },
                            "wrapperColOffset": 0,
                            "wrapperColSpan": 0,
                          },
                          "title": "",
                        },
                        Object {
                          "children": Array [
                            Object {
                              "children": Array [
                                Object {
                                  "componentName": "TextField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cc8",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "addonAfter": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "addonBefore": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "autoFocus": false,
                                    "autoHeight": false,
                                    "behavior": "NORMAL",
                                    "cutString": false,
                                    "fieldId": "textField_k1ow3h23",
                                    "fieldName": "leader",
                                    "hasClear": false,
                                    "hasLimitHint": false,
                                    "htmlType": "input",
                                    "label": Object {
                                      "en-US": "TextField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "主管",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "placeholder": Object {
                                      "en-US": "please input",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请输入",
                                    },
                                    "rows": 4,
                                    "size": "medium",
                                    "state": "",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "trim": false,
                                    "validation": Array [],
                                    "value": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                              ],
                              "componentName": "Column",
                              "condition": true,
                              "conditionGroup": "",
                              "hidden": false,
                              "id": "node_k1ow3cc6",
                              "isLocked": false,
                              "props": Object {
                                "__style__": Object {},
                                "colSpan": "",
                                "fieldId": "column_k1p1bnjo",
                              },
                              "title": "",
                            },
                            Object {
                              "children": Array [
                                Object {
                                  "componentName": "TextField",
                                  "condition": true,
                                  "conditionGroup": "",
                                  "hidden": false,
                                  "id": "node_k1ow3cc9",
                                  "isLocked": false,
                                  "props": Object {
                                    "__category__": "form",
                                    "__style__": Object {},
                                    "__useMediator": "value",
                                    "addonAfter": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "addonBefore": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "autoFocus": false,
                                    "autoHeight": false,
                                    "behavior": "NORMAL",
                                    "cutString": false,
                                    "fieldId": "textField_k1ow3h24",
                                    "fieldName": "hrg",
                                    "hasClear": false,
                                    "hasLimitHint": false,
                                    "htmlType": "input",
                                    "label": Object {
                                      "en-US": "TextField",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "HRG",
                                    },
                                    "labelAlign": "top",
                                    "labelColOffset": 0,
                                    "labelColSpan": 4,
                                    "labelTextAlign": "right",
                                    "labelTipsIcon": "",
                                    "labelTipsText": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "labelTipsTypes": "none",
                                    "placeholder": Object {
                                      "en-US": "please input",
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "请输入",
                                    },
                                    "rows": 4,
                                    "size": "medium",
                                    "state": "",
                                    "tips": Object {
                                      "en-US": "",
                                      "type": "i18n",
                                      "zh-CN": "",
                                    },
                                    "trim": false,
                                    "validation": Array [],
                                    "value": Object {
                                      "type": "i18n",
                                      "use": "zh-CN",
                                      "zh-CN": "",
                                    },
                                    "wrapperColOffset": 0,
                                    "wrapperColSpan": 0,
                                  },
                                  "title": "",
                                },
                              ],
                              "componentName": "Column",
                              "condition": true,
                              "conditionGroup": "",
                              "hidden": false,
                              "id": "node_k1ow3cc7",
                              "isLocked": false,
                              "props": Object {
                                "__style__": Object {},
                                "colSpan": "",
                                "fieldId": "column_k1p1bnjp",
                              },
                              "title": "",
                            },
                          ],
                          "componentName": "ColumnsLayout",
                          "condition": true,
                          "conditionGroup": "",
                          "hidden": false,
                          "id": "node_k1ow3cc5",
                          "isLocked": false,
                          "props": Object {
                            "__style__": Object {},
                            "columnGap": "20",
                            "fieldId": "columns_k1ow3h22",
                            "layout": "6:6",
                            "rowGap": 0,
                          },
                          "title": "",
                        },
                      ],
                      "componentName": "CardContent",
                      "condition": true,
                      "conditionGroup": "",
                      "hidden": false,
                      "id": "node_k1ow3cbm",
                      "isLocked": false,
                      "props": Object {},
                      "title": "",
                    },
                  ],
                  "componentName": "Card",
                  "condition": true,
                  "conditionGroup": "",
                  "hidden": false,
                  "id": "node_k1ow3cbl",
                  "isLocked": false,
                  "props": Object {
                    "__slot__extra": false,
                    "__slot__subTitle": false,
                    "__slot__title": false,
                    "__style__": ":root {
  margin-bottom: 12px;
}",
                    "className": "card_kgaqfbm6",
                    "contentHeight": "",
                    "dividerNoInset": false,
                    "extra": Object {
                      "type": "i18n",
                      "use": "zh-CN",
                      "zh-CN": "",
                    },
                    "fieldId": "card_k1ow3h1m",
                    "showHeadDivider": true,
                    "showTitleBullet": true,
                    "subTitle": Object {
                      "en-US": "",
                      "type": "i18n",
                      "use": "zh-CN",
                      "zh-CN": "",
                    },
                    "title": Object {
                      "en-US": "Title",
                      "type": "i18n",
                      "use": "zh-CN",
                      "zh-CN": "部门信息",
                    },
                  },
                  "title": "",
                },
                Object {
                  "children": Array [
                    Object {
                      "componentName": "Button",
                      "condition": true,
                      "conditionGroup": "",
                      "hidden": false,
                      "id": "node_k1ow3cbn",
                      "isLocked": false,
                      "props": Object {
                        "__style__": ":root {
  margin-right: 16px;
  width: 80px
}",
                        "baseIcon": "",
                        "behavior": "NORMAL",
                        "className": "button_kgaqfbm7",
                        "content": Object {
                          "en-US": "Button",
                          "type": "i18n",
                          "use": "zh-CN",
                          "zh-CN": "提交",
                        },
                        "fieldId": "button_k1ow3h1n",
                        "loading": false,
                        "onClick": Object {
                          "events": Array [
                            Object {
                              "id": "submit",
                              "name": "submit",
                              "params": Object {},
                              "type": "actionRef",
                              "uuid": "1570966253282_0",
                            },
                          ],
                          "rawType": "events",
                          "type": "JSExpression",
                          "value": "this.utils.legaoBuiltin.execEventFlow.bind(this, [this.submit])",
                        },
                        "otherIcon": "",
                        "size": "medium",
                        "triggerEventsWhenLoading": false,
                        "type": "primary",
                      },
                      "title": "",
                    },
                    Object {
                      "componentName": "Button",
                      "condition": true,
                      "conditionGroup": "",
                      "hidden": false,
                      "id": "node_k1ow3cbp",
                      "isLocked": false,
                      "props": Object {
                        "__style__": ":root {
  width: 80px;
}",
                        "baseIcon": "",
                        "behavior": "NORMAL",
                        "className": "button_kgaqfbm8",
                        "content": Object {
                          "en-US": "Button",
                          "type": "i18n",
                          "use": "zh-CN",
                          "zh-CN": "取消",
                        },
                        "fieldId": "button_k1ow3h1p",
                        "greeting": Object {
                          "value": Array [
                            Object {
                              "componentName": "Text",
                              "props": Object {},
                            },
                          ],
                        },
                        "loading": false,
                        "otherIcon": "",
                        "size": "medium",
                        "triggerEventsWhenLoading": false,
                        "type": "normal",
                      },
                      "title": "",
                    },
                  ],
                  "componentName": "Div",
                  "condition": true,
                  "conditionGroup": "",
                  "hidden": false,
                  "id": "node_k1ow3cbo",
                  "isLocked": false,
                  "props": Object {
                    "__style__": ":root {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: #fff;
  padding: 20px 0;
}",
                    "behavior": "NORMAL",
                    "className": "div_kgaqfbm9",
                    "customClassName": "",
                    "events": Object {},
                    "fieldId": "div_k1ow3h1o",
                    "useFieldIdAsDomId": false,
                  },
                  "title": "",
                },
              ],
              "componentName": "Form",
              "condition": true,
              "conditionGroup": "",
              "extraPropA": "extraPropA",
              "hidden": false,
              "id": "form",
              "isLocked": false,
              "props": Object {
                "__style__": Object {},
                "autoUnmount": true,
                "autoValidate": true,
                "behavior": "NORMAL",
                "dataSource": Object {
                  "type": "variable",
                  "variable": "state.formData",
                },
                "fieldId": "form",
                "fieldOptions": Object {},
                "labelAlign": "top",
                "obj": Object {
                  "a": 1,
                  "b": false,
                  "c": "string",
                },
                "scrollToFirstError": true,
                "size": "medium",
                "slotA": "",
              },
              "title": "",
            },
          ],
          "componentName": "RootContent",
          "condition": true,
          "conditionGroup": "",
          "hidden": false,
          "id": "node_k1ow3cbb",
          "isLocked": false,
          "props": Object {
            "contentBgColor": "transparent",
            "contentMargin": "20",
            "contentPadding": "0",
          },
          "title": "",
        },
        Object {
          "componentName": "RootFooter",
          "condition": true,
          "conditionGroup": "",
          "hidden": false,
          "id": "node_k1ow3cbc",
          "isLocked": false,
          "props": Object {},
          "title": "",
        },
      ],
      "componentName": "Page",
      "condition": true,
      "conditionGroup": "",
      "css": "body{background-color:#f2f3f5}.card_kgaqfbm5 {
  margin-bottom: 12px;
}.card_kgaqfbm6 {
  margin-bottom: 12px;
}.button_kgaqfbm7 {
  margin-right: 16px;
  width: 80px
}.button_kgaqfbm8 {
  width: 80px;
}.div_kgaqfbm9 {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: #fff;
  padding: 20px 0;
}",
      "dataSource": Object {
        "globalConfig": Object {
          "fit": Object {
            "compiled": "",
            "error": Object {},
            "source": "",
            "type": "js",
          },
        },
        "list": Array [],
        "offline": Array [],
        "online": Array [],
        "sync": true,
      },
      "hidden": false,
      "i18n": Object {
        "en-US": Object {
          "i18n-jwg27yo3": "China",
          "i18n-jwg27yo4": "Hello",
        },
        "zh-CN": Object {
          "i18n-jwg27yo3": "中国",
          "i18n-jwg27yo4": "你好",
        },
      },
      "id": "page",
      "isLocked": false,
      "lifeCycles": Object {
        "constructor": Object {
          "compiled": "function constructor() {
var module = { exports: {} };
var _this = this;
this.__initMethods__(module.exports, module);
Object.keys(module.exports).forEach(function(item) {
  if(typeof module.exports[item] === 'function'){
    _this[item] = module.exports[item];
  }
});

}",
          "source": "function constructor() {
var module = { exports: {} };
var _this = this;
this.__initMethods__(module.exports, module);
Object.keys(module.exports).forEach(function(item) {
  if(typeof module.exports[item] === 'function'){
    _this[item] = module.exports[item];
  }
});

}",
          "type": "js",
        },
      },
      "methods": Object {
        "__initMethods__": Object {
          "compiled": "function (exports, module) { /*set actions code here*/ }",
          "source": "function (exports, module) { /*set actions code here*/ }",
          "type": "js",
        },
      },
      "props": Object {
        "className": "page_kgaqfbm4",
        "containerStyle": Object {},
        "extensions": Object {
          "启用页头": true,
        },
        "pageStyle": Object {
          "backgroundColor": "#f2f3f5",
        },
        "templateVersion": "1.0.0",
      },
      "title": "hey, i' a page!",
    },
  ],
  "utils": undefined,
}
`;

exports[`document-model 测试 各种方法测试 2`] = `null`;
