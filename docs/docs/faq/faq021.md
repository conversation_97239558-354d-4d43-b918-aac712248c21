---
title: 提交 PR 时，明明签署过 CLA，仍被提示需要签署
sidebar_position: 21
tags: [FAQ]
---
## 问题原因

原因是：git commit 时，本地 git config 配置的 email 与 github 账号的 email 不一致，导致未被识别出来，检查方式

```bash
git config user.email
```

## 解决办法

如何配置，可参考 [is-it-possible-to-have-different-git-configuration-for-different-projects](https://stackoverflow.com/questions/8801729/is-it-possible-to-have-different-git-configuration-for-different-projects)

配置好正确的 email 之后，已提交的代码需要以新的配置重新提交才可生效。